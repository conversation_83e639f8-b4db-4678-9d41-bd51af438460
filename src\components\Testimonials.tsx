import React from 'react'
import { motion } from 'framer-motion'

const Testimonials = () => {
  const testimonials = [
    {
      id: 1,
      name: "Dr. <PERSON>",
      role: "Computer Science Professor, NUML",
      message: "<PERSON><PERSON> consistently demonstrates exceptional problem-solving skills and a deep understanding of software engineering principles. His work on the Hackforge event showcased his leadership abilities.",
      avatar: "AH",
      rating: 5
    },
    {
      id: 2,
      name: "<PERSON>",
      role: "Team Lead, Postex",
      message: "Working with <PERSON><PERSON> as a Product Debriefing Coordinator was a pleasure. His attention to detail and systematic approach to documentation significantly improved our operational efficiency.",
      avatar: "SK",
      rating: 5
    },
    {
      id: 3,
      name: "<PERSON>",
      role: "Senior Developer, Tech Solutions",
      message: "<PERSON><PERSON>'s expertise in both MERN stack and .NET development is impressive. His clean code practices and scalable solutions make him a valuable team member for any project.",
      avatar: "MA",
      rating: 5
    }
  ]

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        delayChildren: 0.3,
        staggerChildren: 0.2
      }
    }
  }

  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        duration: 0.5
      }
    }
  }

  const cardVariants = {
    hidden: { y: 50, opacity: 0, scale: 0.9 },
    visible: {
      y: 0,
      opacity: 1,
      scale: 1,
      transition: {
        duration: 0.6,
        type: "spring",
        stiffness: 100
      }
    }
  }

  return (
    <section id="testimonials" className="py-20 bg-gray-800">
      <div className="max-w-6xl mx-auto px-4">
        <motion.div
          className="text-center mb-16"
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true }}
          variants={containerVariants}
        >
          <motion.h2
            className="text-4xl md:text-5xl font-bold text-white mb-4"
            variants={itemVariants}
          >
            What Others Say
          </motion.h2>
          <motion.div
            className="w-24 h-1 bg-gradient-to-r from-purple-500 to-pink-500 mx-auto mb-4"
            variants={itemVariants}
          />
          <motion.p
            className="text-gray-400 max-w-2xl mx-auto"
            variants={itemVariants}
          >
            Feedback from colleagues, professors, and team members I've had the pleasure to work with.
          </motion.p>
        </motion.div>

        <motion.div
          className="grid md:grid-cols-2 lg:grid-cols-3 gap-8"
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true }}
          variants={containerVariants}
        >
          {testimonials.map((testimonial) => (
            <motion.div
              key={testimonial.id}
              className="bg-gray-900 p-6 rounded-lg shadow-lg relative"
              variants={cardVariants}
              whileHover={{ 
                y: -5, 
                scale: 1.02,
                transition: { duration: 0.2 }
              }}
            >
              {/* Quote icon */}
              <div className="absolute top-4 right-4 text-purple-500 text-2xl opacity-50">
                "
              </div>

              {/* Rating stars */}
              <div className="flex mb-4">
                {[...Array(testimonial.rating)].map((_, i) => (
                  <motion.span
                    key={i}
                    className="text-yellow-400 text-lg"
                    initial={{ opacity: 0, scale: 0 }}
                    animate={{ opacity: 1, scale: 1 }}
                    transition={{ delay: i * 0.1 + 0.5 }}
                  >
                    ⭐
                  </motion.span>
                ))}
              </div>

              {/* Message */}
              <p className="text-gray-300 mb-6 leading-relaxed italic">
                "{testimonial.message}"
              </p>

              {/* Author info */}
              <div className="flex items-center">
                <motion.div
                  className="w-12 h-12 bg-gradient-to-br from-purple-500 to-pink-500 rounded-full flex items-center justify-center text-white font-bold mr-4"
                  whileHover={{ rotate: 360 }}
                  transition={{ duration: 0.5 }}
                >
                  {testimonial.avatar}
                </motion.div>
                <div>
                  <h4 className="text-white font-semibold">
                    {testimonial.name}
                  </h4>
                  <p className="text-purple-400 text-sm">
                    {testimonial.role}
                  </p>
                </div>
              </div>

              {/* Decorative elements */}
              <div className="absolute bottom-0 left-0 w-full h-1 bg-gradient-to-r from-purple-500 to-pink-500 rounded-b-lg" />
            </motion.div>
          ))}
        </motion.div>

        {/* Call to action */}
        <motion.div
          className="text-center mt-12"
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true }}
          variants={itemVariants}
        >
          <motion.p
            className="text-gray-400 mb-6"
            variants={itemVariants}
          >
            Want to work together? Let's create something amazing!
          </motion.p>
          <motion.a
            href="#contact"
            className="inline-flex items-center px-6 py-3 bg-gradient-to-r from-purple-500 to-pink-500 text-white font-semibold rounded-lg hover:shadow-lg transition-all duration-300"
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
            Get In Touch
          </motion.a>
        </motion.div>
      </div>
    </section>
  )
}

export default Testimonials
