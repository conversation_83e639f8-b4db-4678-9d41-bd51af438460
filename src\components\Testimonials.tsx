import { motion } from 'framer-motion'

const Testimonials = () => {
  const testimonials = [
    {
      id: 1,
      name: "Dr. <PERSON>",
      role: "Computer Science Professor, NUML",
      message: "<PERSON><PERSON> consistently demonstrates exceptional problem-solving skills and a deep understanding of software engineering principles. His work on the Hackforge event showcased his leadership abilities.",
      avatar: "AH",
      rating: 5
    },
    {
      id: 2,
      name: "<PERSON>",
      role: "Team Lead, Postex",
      message: "Working with <PERSON><PERSON> as a Product Debriefing Coordinator was a pleasure. His attention to detail and systematic approach to documentation significantly improved our operational efficiency.",
      avatar: "SK",
      rating: 5
    },
    {
      id: 3,
      name: "<PERSON>",
      role: "Senior Developer, Tech Solutions",
      message: "<PERSON><PERSON>'s expertise in both MERN stack and .NET development is impressive. His clean code practices and scalable solutions make him a valuable team member for any project.",
      avatar: "MA",
      rating: 5
    }
  ]

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        delayChildren: 0.3,
        staggerChildren: 0.2
      }
    }
  }

  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        duration: 0.5
      }
    }
  }

  const cardVariants = {
    hidden: { y: 50, opacity: 0, scale: 0.9 },
    visible: {
      y: 0,
      opacity: 1,
      scale: 1,
      transition: {
        duration: 0.6,
        type: "spring" as const,
        stiffness: 100
      }
    }
  }

  return (
    <section id="testimonials" className="bg-secondary" style={{ padding: 'var(--space-5xl) 0' }}>
      <div className="container-responsive">
        <motion.div
          className="text-center"
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true }}
          variants={containerVariants}
          style={{ marginBottom: 'var(--space-4xl)' }}
        >
          <motion.h2
            className="text-responsive-5xl font-bold text-primary"
            variants={itemVariants}
            style={{ marginBottom: 'var(--space-md)' }}
          >
            What Others Say
          </motion.h2>
          <motion.div
            className="mx-auto"
            variants={itemVariants}
            style={{
              width: '96px',
              height: '4px',
              background: 'var(--gradient-primary)',
              borderRadius: 'var(--radius-full)',
              marginBottom: 'var(--space-md)',
            }}
          />
          <motion.p
            className="text-secondary max-w-2xl mx-auto text-responsive-base"
            variants={itemVariants}
          >
            Feedback from colleagues, professors, and team members I've had the pleasure to work with.
          </motion.p>
        </motion.div>

        <motion.div
          className="grid-responsive-3"
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true }}
          variants={containerVariants}
        >
          {testimonials.map((testimonial) => (
            <motion.div
              key={testimonial.id}
              className="card-base relative touch-target"
              variants={cardVariants}
              whileHover={{
                y: -5,
                scale: 1.02,
                transition: { duration: 0.2 }
              }}
              style={{ padding: 'var(--space-lg)' }}
            >
              {/* Quote icon */}
              <div
                className="absolute text-brand-primary opacity-50"
                style={{
                  top: 'var(--space-md)',
                  right: 'var(--space-md)',
                  fontSize: 'var(--text-2xl)',
                }}
              >
                "
              </div>

              {/* Rating stars */}
              <div
                className="flex"
                style={{
                  marginBottom: 'var(--space-md)',
                  gap: 'var(--space-xs)',
                }}
              >
                {[...Array(testimonial.rating)].map((_, i) => (
                  <motion.span
                    key={i}
                    className="text-responsive-base"
                    style={{ color: 'var(--color-warning)' }}
                    initial={{ opacity: 0, scale: 0 }}
                    animate={{ opacity: 1, scale: 1 }}
                    transition={{ delay: i * 0.1 + 0.5 }}
                  >
                    ⭐
                  </motion.span>
                ))}
              </div>

              {/* Message */}
              <p
                className="text-secondary leading-relaxed italic text-responsive-sm"
                style={{ marginBottom: 'var(--space-lg)' }}
              >
                "{testimonial.message}"
              </p>

              {/* Author info */}
              <div
                className="flex items-center"
                style={{ gap: 'var(--space-md)' }}
              >
                <motion.div
                  className="rounded-full flex items-center justify-center text-inverse font-bold"
                  whileHover={{ rotate: 360 }}
                  transition={{ duration: 0.5 }}
                  style={{
                    width: '48px',
                    height: '48px',
                    background: 'var(--gradient-primary)',
                    fontSize: 'var(--text-base)',
                  }}
                >
                  {testimonial.avatar}
                </motion.div>
                <div>
                  <h4 className="text-primary font-semibold text-responsive-base">
                    {testimonial.name}
                  </h4>
                  <p className="text-brand-primary text-responsive-sm">
                    {testimonial.role}
                  </p>
                </div>
              </div>

              {/* Decorative elements */}
              <div
                className="absolute bottom-0 left-0 w-full"
                style={{
                  height: '4px',
                  background: 'var(--gradient-primary)',
                  borderBottomLeftRadius: 'var(--radius-lg)',
                  borderBottomRightRadius: 'var(--radius-lg)',
                }}
              />
            </motion.div>
          ))}
        </motion.div>

        {/* Call to action */}
        <motion.div
          className="text-center"
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true }}
          variants={itemVariants}
          style={{ marginTop: 'var(--space-3xl)' }}
        >
          <motion.p
            className="text-secondary text-responsive-base"
            variants={itemVariants}
            style={{ marginBottom: 'var(--space-lg)' }}
          >
            Want to work together? Let's create something amazing!
          </motion.p>
          <motion.a
            href="#contact"
            className="btn-primary inline-flex items-center touch-target"
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            style={{ textDecoration: 'none' }}
          >
            Get In Touch
          </motion.a>
        </motion.div>
      </div>
    </section>
  )
}

export default Testimonials
