@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
@import "tailwindcss";

@layer base {
  body {
    font-family: 'Inter', sans-serif;
    transition: background-color 0.3s ease, color 0.3s ease;
  }

  html {
    scroll-behavior: smooth;
  }

  /* CSS Custom Properties for Design System */
  :root {
    /* Spacing Scale */
    --space-xs: 0.25rem;    /* 4px */
    --space-sm: 0.5rem;     /* 8px */
    --space-md: 1rem;       /* 16px */
    --space-lg: 1.5rem;     /* 24px */
    --space-xl: 2rem;       /* 32px */
    --space-2xl: 3rem;      /* 48px */
    --space-3xl: 4rem;      /* 64px */
    --space-4xl: 6rem;      /* 96px */
    --space-5xl: 8rem;      /* 128px */

    /* Typography Scale */
    --text-xs: 0.75rem;     /* 12px */
    --text-sm: 0.875rem;    /* 14px */
    --text-base: 1rem;      /* 16px */
    --text-lg: 1.125rem;    /* 18px */
    --text-xl: 1.25rem;     /* 20px */
    --text-2xl: 1.5rem;     /* 24px */
    --text-3xl: 1.875rem;   /* 30px */
    --text-4xl: 2.25rem;    /* 36px */
    --text-5xl: 3rem;       /* 48px */
    --text-6xl: 3.75rem;    /* 60px */
    --text-7xl: 4.5rem;     /* 72px */

    /* Border Radius */
    --radius-sm: 0.25rem;   /* 4px */
    --radius-md: 0.5rem;    /* 8px */
    --radius-lg: 0.75rem;   /* 12px */
    --radius-xl: 1rem;      /* 16px */
    --radius-2xl: 1.5rem;   /* 24px */
    --radius-full: 9999px;

    /* Shadows */
    --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
    --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
    --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
    --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
    --shadow-2xl: 0 25px 50px -12px rgb(0 0 0 / 0.25);

    /* Transitions */
    --transition-fast: 150ms ease;
    --transition-normal: 300ms ease;
    --transition-slow: 500ms ease;

    /* Z-Index Scale */
    --z-dropdown: 1000;
    --z-sticky: 1020;
    --z-fixed: 1030;
    --z-modal: 1040;
    --z-popover: 1050;
    --z-tooltip: 1060;
  }

  /* Light mode color variables */
  .light {
    /* Background Colors */
    --bg-primary: #ffffff;
    --bg-secondary: #f8fafc;
    --bg-tertiary: #f1f5f9;
    --bg-quaternary: #e2e8f0;
    --bg-overlay: rgba(255, 255, 255, 0.9);
    --bg-card: #ffffff;
    --bg-input: #ffffff;
    --bg-hover: #f1f5f9;

    /* Text Colors */
    --text-primary: #1e293b;
    --text-secondary: #475569;
    --text-tertiary: #64748b;
    --text-quaternary: #64748b;
    --text-inverse: #ffffff;
    --text-muted: #64748b;

    /* Border Colors */
    --border-primary: #e2e8f0;
    --border-secondary: #cbd5e1;
    --border-tertiary: #94a3b8;
    --border-focus: #8b5cf6;

    /* Brand Colors */
    --color-primary: #8b5cf6;
    --color-primary-hover: #7c3aed;
    --color-primary-light: #a78bfa;
    --color-secondary: #ec4899;
    --color-secondary-hover: #db2777;
    --color-secondary-light: #f472b6;

    /* Status Colors */
    --color-success: #10b981;
    --color-warning: #f59e0b;
    --color-error: #ef4444;
    --color-info: #3b82f6;

    /* Gradient Colors */
    --gradient-primary: linear-gradient(135deg, #8b5cf6 0%, #ec4899 100%);
    --gradient-secondary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    --gradient-hero: linear-gradient(135deg, #ddd6fe 0%, #fce7f3 50%, #fed7e2 100%);
  }

  /* Dark mode color variables */
  .dark {
    /* Background Colors */
    --bg-primary: #0f172a;
    --bg-secondary: #1e293b;
    --bg-tertiary: #334155;
    --bg-quaternary: #475569;
    --bg-overlay: rgba(15, 23, 42, 0.9);
    --bg-card: #1e293b;
    --bg-input: #334155;
    --bg-hover: #334155;

    /* Text Colors */
    --text-primary: #f8fafc;
    --text-secondary: #e2e8f0;
    --text-tertiary: #cbd5e1;
    --text-quaternary: #94a3b8;
    --text-inverse: #1e293b;
    --text-muted: #94a3b8;

    /* Border Colors */
    --border-primary: #334155;
    --border-secondary: #475569;
    --border-tertiary: #64748b;
    --border-focus: #a78bfa;

    /* Brand Colors */
    --color-primary: #a78bfa;
    --color-primary-hover: #8b5cf6;
    --color-primary-light: #c4b5fd;
    --color-secondary: #f472b6;
    --color-secondary-hover: #ec4899;
    --color-secondary-light: #f9a8d4;

    /* Status Colors */
    --color-success: #34d399;
    --color-warning: #fbbf24;
    --color-error: #f87171;
    --color-info: #60a5fa;

    /* Gradient Colors */
    --gradient-primary: linear-gradient(135deg, #a78bfa 0%, #f472b6 100%);
    --gradient-secondary: linear-gradient(135deg, #8b5cf6 0%, #ec4899 100%);
    --gradient-hero: linear-gradient(135deg, #0f172a 0%, #581c87 50%, #7e22ce 100%);
  }
}

@layer components {
  /* Utility classes using CSS variables */
  .bg-primary { background-color: var(--bg-primary); }
  .bg-secondary { background-color: var(--bg-secondary); }
  .bg-tertiary { background-color: var(--bg-tertiary); }
  .bg-quaternary { background-color: var(--bg-quaternary); }
  .bg-card { background-color: var(--bg-card); }
  .bg-overlay { background-color: var(--bg-overlay); }
  .bg-hover { background-color: var(--bg-hover); }

  .text-primary { color: var(--text-primary); }
  .text-secondary { color: var(--text-secondary); }
  .text-tertiary { color: var(--text-tertiary); }
  .text-quaternary { color: var(--text-quaternary); }
  .text-inverse { color: var(--text-inverse); }
  .text-muted { color: var(--text-muted); }

  .border-primary { border-color: var(--border-primary); }
  .border-secondary { border-color: var(--border-secondary); }
  .border-tertiary { border-color: var(--border-tertiary); }
  .border-focus { border-color: var(--border-focus); }

  .text-brand-primary { color: var(--color-primary); }
  .text-brand-secondary { color: var(--color-secondary); }
  .bg-brand-primary { background-color: var(--color-primary); }
  .bg-brand-secondary { background-color: var(--color-secondary); }

  .bg-gradient-primary { background: var(--gradient-primary); }
  .bg-gradient-secondary { background: var(--gradient-secondary); }
  .bg-gradient-hero { background: var(--gradient-hero); }

  .shadow-custom-sm { box-shadow: var(--shadow-sm); }
  .shadow-custom-md { box-shadow: var(--shadow-md); }
  .shadow-custom-lg { box-shadow: var(--shadow-lg); }
  .shadow-custom-xl { box-shadow: var(--shadow-xl); }
  .shadow-custom-2xl { box-shadow: var(--shadow-2xl); }

  .transition-fast { transition: all var(--transition-fast); }
  .transition-normal { transition: all var(--transition-normal); }
  .transition-slow { transition: all var(--transition-slow); }

  .rounded-custom-sm { border-radius: var(--radius-sm); }
  .rounded-custom-md { border-radius: var(--radius-md); }
  .rounded-custom-lg { border-radius: var(--radius-lg); }
  .rounded-custom-xl { border-radius: var(--radius-xl); }
  .rounded-custom-2xl { border-radius: var(--radius-2xl); }

  /* Responsive typography utilities */
  .text-responsive-xs { font-size: clamp(var(--text-xs), 2vw, var(--text-sm)); }
  .text-responsive-sm { font-size: clamp(var(--text-sm), 2.5vw, var(--text-base)); }
  .text-responsive-base { font-size: clamp(var(--text-base), 3vw, var(--text-lg)); }
  .text-responsive-lg { font-size: clamp(var(--text-lg), 3.5vw, var(--text-xl)); }
  .text-responsive-xl { font-size: clamp(var(--text-xl), 4vw, var(--text-2xl)); }
  .text-responsive-2xl { font-size: clamp(var(--text-2xl), 4.5vw, var(--text-3xl)); }
  .text-responsive-3xl { font-size: clamp(var(--text-3xl), 5vw, var(--text-4xl)); }
  .text-responsive-4xl { font-size: clamp(var(--text-4xl), 6vw, var(--text-5xl)); }
  .text-responsive-5xl { font-size: clamp(var(--text-5xl), 7vw, var(--text-6xl)); }
  .text-responsive-6xl { font-size: clamp(var(--text-6xl), 8vw, var(--text-7xl)); }

  /* Responsive spacing utilities */
  .space-responsive-sm { margin: clamp(var(--space-sm), 2vw, var(--space-md)); }
  .space-responsive-md { margin: clamp(var(--space-md), 3vw, var(--space-lg)); }
  .space-responsive-lg { margin: clamp(var(--space-lg), 4vw, var(--space-xl)); }
  .space-responsive-xl { margin: clamp(var(--space-xl), 5vw, var(--space-2xl)); }

  .p-responsive-sm { padding: clamp(var(--space-sm), 2vw, var(--space-md)); }
  .p-responsive-md { padding: clamp(var(--space-md), 3vw, var(--space-lg)); }
  .p-responsive-lg { padding: clamp(var(--space-lg), 4vw, var(--space-xl)); }
  .p-responsive-xl { padding: clamp(var(--space-xl), 5vw, var(--space-2xl)); }

  /* Custom animations */
  @keyframes fadeInUp {
    from {
      opacity: 0;
      transform: translateY(30px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  @keyframes slideInLeft {
    from {
      opacity: 0;
      transform: translateX(-30px);
    }
    to {
      opacity: 1;
      transform: translateX(0);
    }
  }

  @keyframes slideInRight {
    from {
      opacity: 0;
      transform: translateX(30px);
    }
    to {
      opacity: 1;
      transform: translateX(0);
    }
  }

  .animate-fadeInUp {
    animation: fadeInUp 0.6s ease-out;
  }

  .animate-slideInLeft {
    animation: slideInLeft 0.6s ease-out;
  }

  .animate-slideInRight {
    animation: slideInRight 0.6s ease-out;
  }

  /* Responsive container utilities */
  .container-responsive {
    width: 100%;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 var(--space-md);
  }

  @media (min-width: 640px) {
    .container-responsive {
      padding: 0 var(--space-lg);
    }
  }

  @media (min-width: 1024px) {
    .container-responsive {
      padding: 0 var(--space-xl);
    }
  }

  /* Responsive grid utilities */
  .grid-responsive-1 {
    display: grid;
    grid-template-columns: 1fr;
    gap: var(--space-md);
  }

  .grid-responsive-2 {
    display: grid;
    grid-template-columns: 1fr;
    gap: var(--space-md);
  }

  @media (min-width: 768px) {
    .grid-responsive-2 {
      grid-template-columns: repeat(2, 1fr);
      gap: var(--space-lg);
    }
  }

  .grid-responsive-3 {
    display: grid;
    grid-template-columns: 1fr;
    gap: var(--space-md);
  }

  @media (min-width: 768px) {
    .grid-responsive-3 {
      grid-template-columns: repeat(2, 1fr);
    }
  }

  @media (min-width: 1024px) {
    .grid-responsive-3 {
      grid-template-columns: repeat(3, 1fr);
      gap: var(--space-lg);
    }
  }

  .grid-responsive-4 {
    display: grid;
    grid-template-columns: 1fr;
    gap: var(--space-md);
  }

  @media (min-width: 640px) {
    .grid-responsive-4 {
      grid-template-columns: repeat(2, 1fr);
    }
  }

  @media (min-width: 1024px) {
    .grid-responsive-4 {
      grid-template-columns: repeat(4, 1fr);
      gap: var(--space-lg);
    }
  }

  /* Mobile optimizations */
  .touch-target {
    min-height: 44px;
    min-width: 44px;
  }

  /* Smooth scrolling for mobile */
  @media (max-width: 768px) {
    html {
      scroll-behavior: smooth;
      -webkit-overflow-scrolling: touch;
    }

    /* Prevent horizontal scroll */
    body {
      overflow-x: hidden;
    }
  }

  /* Line clamp utility */
  .line-clamp-3 {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  /* Card component base styles */
  .card-base {
    background-color: var(--bg-card);
    border: 1px solid var(--border-primary);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-md);
    transition: var(--transition-normal);
  }

  .card-base:hover {
    box-shadow: var(--shadow-lg);
    transform: translateY(-2px);
  }

  /* Button component base styles */
  .btn-primary {
    background: var(--gradient-primary);
    color: var(--text-inverse);
    border: none;
    border-radius: var(--radius-lg);
    padding: var(--space-md) var(--space-lg);
    font-weight: 600;
    transition: var(--transition-normal);
    cursor: pointer;
  }

  .btn-primary:hover {
    transform: translateY(-1px);
    box-shadow: var(--shadow-lg);
  }

  .btn-secondary {
    background-color: transparent;
    color: var(--color-primary);
    border: 2px solid var(--color-primary);
    border-radius: var(--radius-lg);
    padding: var(--space-md) var(--space-lg);
    font-weight: 600;
    transition: var(--transition-normal);
    cursor: pointer;
  }

  .btn-secondary:hover {
    background-color: var(--color-primary);
    color: var(--text-inverse);
  }
}
