import React from 'react'
import { motion } from 'framer-motion'

const Skills = () => {
  const skillCategories = [
    {
      title: "Languages",
      skills: [
        { name: "JavaScript", level: 90 },
        { name: "Java", level: 85 },
        { name: "C/C++", level: 80 },
        { name: "SQL", level: 85 },
        { name: "C#", level: 75 }
      ]
    },
    {
      title: "Frameworks",
      skills: [
        { name: "React.js", level: 90 },
        { name: "Node.js", level: 85 },
        { name: "Express.js", level: 85 },
        { name: ".NET", level: 80 },
        { name: "Tailwind CSS", level: 90 }
      ]
    },
    {
      title: "Database",
      skills: [
        { name: "MongoDB", level: 85 },
        { name: "SQL Server", level: 80 },
        { name: "MySQL", level: 75 },
        { name: "Database Design", level: 80 },
        { name: "Query Optimization", level: 70 }
      ]
    },
    {
      title: "Tools & Others",
      skills: [
        { name: "<PERSON><PERSON>", level: 90 },
        { name: "GitHub", level: 90 },
        { name: "VS Code", level: 95 },
        { name: "<PERSON><PERSON>", level: 85 },
        { name: "npm/yarn", level: 85 }
      ]
    }
  ]

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        delayChildren: 0.3,
        staggerChildren: 0.1
      }
    }
  }

  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        duration: 0.5
      }
    }
  }

  const skillVariants = {
    hidden: { width: 0 },
    visible: (level: number) => ({
      width: `${level}%`,
      transition: {
        duration: 1,
        delay: 0.5
      }
    })
  }

  return (
    <section id="skills" className="bg-secondary" style={{ padding: 'var(--space-5xl) 0' }}>
      <div className="container-responsive">
        <motion.div
          className="text-center"
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true }}
          variants={containerVariants}
          style={{ marginBottom: 'var(--space-4xl)' }}
        >
          <motion.h2
            className="text-responsive-5xl font-bold text-primary"
            variants={itemVariants}
            style={{ marginBottom: 'var(--space-md)' }}
          >
            Skills & Technologies
          </motion.h2>
          <motion.div
            className="mx-auto"
            variants={itemVariants}
            style={{
              width: '96px',
              height: '4px',
              background: 'var(--gradient-primary)',
              borderRadius: 'var(--radius-full)',
              marginBottom: 'var(--space-md)',
            }}
          />
          <motion.p
            className="text-secondary max-w-2xl mx-auto text-responsive-base"
            variants={itemVariants}
          >
            Here are the technologies and tools I work with to bring ideas to life.
          </motion.p>
        </motion.div>

        <motion.div
          className="grid-responsive-4"
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true }}
          variants={containerVariants}
        >
          {skillCategories.map((category, categoryIndex) => (
            <motion.div
              key={categoryIndex}
              className="card-base touch-target"
              variants={itemVariants}
              style={{ padding: 'var(--space-lg)' }}
            >
              <h3
                className="text-responsive-xl font-semibold text-primary text-center"
                style={{ marginBottom: 'var(--space-lg)' }}
              >
                {category.title}
              </h3>
              <div style={{ display: 'flex', flexDirection: 'column', gap: 'var(--space-md)' }}>
                {category.skills.map((skill, skillIndex) => (
                  <div key={skillIndex}>
                    <div
                      className="flex justify-between items-center"
                      style={{ marginBottom: 'var(--space-sm)' }}
                    >
                      <span className="text-secondary text-responsive-sm font-medium">
                        {skill.name}
                      </span>
                      <span className="text-brand-primary text-responsive-sm">
                        {skill.level}%
                      </span>
                    </div>
                    <div
                      className="w-full rounded-full"
                      style={{
                        height: '8px',
                        backgroundColor: 'var(--bg-tertiary)',
                        borderRadius: 'var(--radius-full)',
                      }}
                    >
                      <motion.div
                        className="rounded-full"
                        variants={skillVariants}
                        custom={skill.level}
                        initial="hidden"
                        whileInView="visible"
                        viewport={{ once: true }}
                        style={{
                          height: '8px',
                          background: 'var(--gradient-primary)',
                          borderRadius: 'var(--radius-full)',
                        }}
                      />
                    </div>
                  </div>
                ))}
              </div>
            </motion.div>
          ))}
        </motion.div>

        {/* Soft Skills Section */}
        <motion.div
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true }}
          variants={containerVariants}
          style={{ marginTop: 'var(--space-4xl)' }}
        >
          <motion.h3
            className="text-responsive-2xl font-semibold text-primary text-center"
            variants={itemVariants}
            style={{ marginBottom: 'var(--space-2xl)' }}
          >
            Soft Skills
          </motion.h3>
          <motion.div
            className="grid-responsive-4"
            variants={containerVariants}
          >
            {[
              "Communication",
              "Documentation",
              "Team Collaboration",
              "Problem Solving",
              "Leadership",
              "Project Management",
              "Quality Assurance",
              "Cross-platform Logic"
            ].map((skill, index) => (
              <motion.div
                key={index}
                className="card-base text-center touch-target"
                variants={itemVariants}
                whileHover={{ scale: 1.05 }}
                style={{ padding: 'var(--space-md)' }}
              >
                <span className="text-secondary font-medium text-responsive-sm">{skill}</span>
              </motion.div>
            ))}
          </motion.div>
        </motion.div>
      </div>
    </section>
  )
}

export default Skills
