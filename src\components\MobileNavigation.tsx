import { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'

const MobileNavigation = () => {
  const [activeSection, setActiveSection] = useState('hero')
  const [isVisible, setIsVisible] = useState(false)

  const navItems = [
    { id: 'hero', icon: '🏠', label: 'Home' },
    { id: 'about', icon: '👨‍💻', label: 'About' },
    { id: 'projects', icon: '💼', label: 'Projects' },
    { id: 'skills', icon: '🛠️', label: 'Skills' },
    { id: 'blog', icon: '📝', label: 'Blog' },
    { id: 'contact', icon: '📧', label: 'Contact' }
  ]

  useEffect(() => {
    const handleScroll = () => {
      // Show navigation after scrolling down a bit
      setIsVisible(window.scrollY > 100)

      // Update active section based on scroll position
      const sections = navItems.map(item => item.id)
      const scrollPosition = window.scrollY + 100

      for (const sectionId of sections) {
        const element = document.getElementById(sectionId === 'hero' ? '' : sectionId)
        if (element) {
          const { offsetTop, offsetHeight } = element
          if (scrollPosition >= offsetTop && scrollPosition < offsetTop + offsetHeight) {
            setActiveSection(sectionId)
            break
          }
        }
      }
    }

    window.addEventListener('scroll', handleScroll)
    return () => window.removeEventListener('scroll', handleScroll)
  }, [])

  const scrollToSection = (sectionId: string) => {
    setActiveSection(sectionId)

    if (sectionId === 'hero') {
      window.scrollTo({ top: 0, behavior: 'smooth' })
    } else {
      const element = document.getElementById(sectionId)
      if (element) {
        element.scrollIntoView({ behavior: 'smooth' })
      }
    }
  }

  const containerVariants = {
    hidden: {
      y: 100,
      opacity: 0,
      scale: 0.8
    },
    visible: {
      y: 0,
      opacity: 1,
      scale: 1,
      transition: {
        type: "spring" as const,
        stiffness: 300,
        damping: 30,
        staggerChildren: 0.1
      }
    },
    exit: {
      y: 100,
      opacity: 0,
      scale: 0.8,
      transition: {
        duration: 0.3
      }
    }
  }

  const itemVariants = {
    hidden: { scale: 0, opacity: 0 },
    visible: {
      scale: 1,
      opacity: 1,
      transition: {
        type: "spring" as const,
        stiffness: 400,
        damping: 25
      }
    }
  }

  const activeIndicatorVariants = {
    initial: { scale: 0 },
    animate: { scale: 1 },
    exit: { scale: 0 }
  }

  return (
    <AnimatePresence>
      {isVisible && (
        <motion.div
          className="fixed left-1/2 transform -translate-x-1/2 md:hidden"
          variants={containerVariants}
          initial="hidden"
          animate="visible"
          exit="exit"
          style={{
            bottom: 'var(--space-lg)',
            zIndex: 'var(--z-fixed)',
          }}
        >
          <div
            className="backdrop-blur-lg shadow-custom-2xl"
            style={{
              backgroundColor: 'var(--bg-overlay)',
              border: '1px solid var(--border-secondary)',
              borderRadius: 'var(--radius-2xl)',
              padding: 'var(--space-md) var(--space-md)',
            }}
          >
            <div className="flex items-center space-x-1">
              {navItems.map((item) => (
                <motion.button
                  key={item.id}
                  onClick={() => scrollToSection(item.id)}
                  className="relative p-3 rounded-xl transition-all duration-300"
                  variants={itemVariants}
                  whileHover={{ scale: 1.1 }}
                  whileTap={{ scale: 0.95 }}
                >
                  {/* Active indicator */}
                  {activeSection === item.id && (
                    <motion.div
                      className="absolute inset-0 bg-gradient-to-r from-purple-500 to-pink-500 rounded-xl"
                      variants={activeIndicatorVariants}
                      initial="initial"
                      animate="animate"
                      exit="exit"
                      layoutId="activeIndicator"
                    />
                  )}
                  
                  {/* Icon */}
                  <motion.div
                    className={`relative z-10 text-lg ${
                      activeSection === item.id 
                        ? 'text-white' 
                        : 'text-gray-400'
                    }`}
                    animate={{
                      scale: activeSection === item.id ? 1.1 : 1,
                      rotate: activeSection === item.id ? [0, -10, 10, 0] : 0
                    }}
                    transition={{ duration: 0.3 }}
                  >
                    {item.icon}
                  </motion.div>

                  {/* Tooltip */}
                  <motion.div
                    className="absolute -top-12 left-1/2 transform -translate-x-1/2 bg-gray-900 text-white text-xs px-2 py-1 rounded-lg opacity-0 pointer-events-none"
                    whileHover={{ opacity: 1, y: -2 }}
                    transition={{ duration: 0.2 }}
                  >
                    {item.label}
                    <div className="absolute top-full left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-2 border-r-2 border-t-2 border-transparent border-t-gray-900" />
                  </motion.div>
                </motion.button>
              ))}
            </div>
          </div>

          {/* Floating particles effect */}
          <div className="absolute inset-0 pointer-events-none">
            {[...Array(3)].map((_, i) => (
              <motion.div
                key={i}
                className="absolute w-1 h-1 bg-purple-400 rounded-full"
                style={{
                  left: `${20 + i * 30}%`,
                  top: `${10 + i * 20}%`,
                }}
                animate={{
                  y: [-5, -15, -5],
                  opacity: [0.3, 0.8, 0.3],
                  scale: [0.5, 1, 0.5]
                }}
                transition={{
                  duration: 2 + i * 0.5,
                  repeat: Infinity,
                  delay: i * 0.3
                }}
              />
            ))}
          </div>
        </motion.div>
      )}
    </AnimatePresence>
  )
}

export default MobileNavigation
