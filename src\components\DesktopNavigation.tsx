import { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'

const DesktopNavigation = () => {
  const [activeSection, setActiveSection] = useState('hero')
  const [isVisible, setIsVisible] = useState(false)
  const [manualOverride, setManualOverride] = useState(false)

  const navItems = [
    { id: 'hero', icon: '🏠', label: 'Home' },
    { id: 'about', icon: '👨‍💻', label: 'About' },
    { id: 'projects', icon: '💼', label: 'Projects' },
    { id: 'github-repos', icon: '🐙', label: 'Repos' },
    { id: 'skills', icon: '🛠️', label: 'Skills' },
    { id: 'education', icon: '🎓', label: 'Experience' },
    { id: 'testimonials', icon: '💬', label: 'Reviews' },
    { id: 'blog', icon: '📝', label: 'Blog' },
    { id: 'contact', icon: '📧', label: 'Contact' }
  ]

  useEffect(() => {
    const handleScroll = () => {
      // Show navigation after scrolling down a bit
      setIsVisible(window.scrollY > 100)

      // Skip auto-detection if manual override is active
      if (manualOverride) return

      // Update active section based on scroll position
      const scrollPosition = window.scrollY + 100
      let currentSection = 'hero'

      // Check if we're at the top of the page
      if (window.scrollY < 50) {
        setActiveSection('hero')
        return
      }

      // Check each section
      const sections = ['about', 'projects', 'github-repos', 'skills', 'education', 'testimonials', 'blog', 'contact']

      for (const sectionId of sections) {
        const element = document.getElementById(sectionId)

        if (element) {
          const rect = element.getBoundingClientRect()
          const elementTop = window.scrollY + rect.top

          if (scrollPosition >= elementTop - 100) {
            currentSection = sectionId
          }
        }
      }

      setActiveSection(currentSection)
    }

    window.addEventListener('scroll', handleScroll)
    return () => window.removeEventListener('scroll', handleScroll)
  }, [manualOverride])

  const scrollToSection = (sectionId: string) => {
    setActiveSection(sectionId)
    setManualOverride(true)

    if (sectionId === 'hero') {
      window.scrollTo({ top: 0, behavior: 'smooth' })
    } else {
      const element = document.getElementById(sectionId)
      if (element) {
        element.scrollIntoView({ behavior: 'smooth' })
      }
    }

    // Reset manual override after scroll completes
    setTimeout(() => {
      setManualOverride(false)
    }, 1000)
  }

  const containerVariants = {
    hidden: {
      y: 100,
      opacity: 0,
      scale: 0.8
    },
    visible: {
      y: 0,
      opacity: 1,
      scale: 1,
      transition: {
        type: "spring" as const,
        stiffness: 300,
        damping: 30,
        staggerChildren: 0.05
      }
    },
    exit: {
      y: 100,
      opacity: 0,
      scale: 0.8,
      transition: {
        duration: 0.3
      }
    }
  }

  const itemVariants = {
    hidden: { scale: 0, opacity: 0 },
    visible: { 
      scale: 1, 
      opacity: 1,
      transition: {
        type: "spring" as const,
        stiffness: 400,
        damping: 25
      }
    }
  }

  const activeIndicatorVariants = {
    initial: { scale: 0 },
    animate: { scale: 1 },
    exit: { scale: 0 }
  }

  return (
    <AnimatePresence>
      {isVisible && (
        <motion.div
          className="fixed bottom-6 left-1/2 transform -translate-x-1/2 hidden md:block"
          variants={containerVariants}
          initial="hidden"
          animate="visible"
          exit="exit"
          style={{
            zIndex: 'var(--z-fixed)',
          }}
        >
          <div
            className="backdrop-blur-lg shadow-custom-2xl"
            style={{
              backgroundColor: 'var(--bg-overlay)',
              border: '1px solid var(--border-secondary)',
              borderRadius: 'var(--radius-2xl)',
              padding: 'var(--space-sm) var(--space-lg)',
            }}
          >
            <div
              className="flex"
              style={{ gap: 'var(--space-xs)' }}
            >
              {navItems.map((item) => (
                <motion.button
                  key={item.id}
                  onClick={() => scrollToSection(item.id)}
                  className="relative rounded-xl transition-normal touch-target group"
                  variants={itemVariants}
                  whileHover={{ scale: 1.15, y: -5 }}
                  whileTap={{ scale: 0.95 }}
                  style={{
                    padding: 'var(--space-sm)',
                    minWidth: '48px',
                    minHeight: '48px',
                    display: 'flex',
                    flexDirection: 'column',
                    alignItems: 'center',
                    justifyContent: 'center',
                    gap: 'var(--space-xs)',
                  }}
                >
                  {/* Active indicator */}
                  {activeSection === item.id && (
                    <motion.div
                      className="absolute inset-0 rounded-xl"
                      variants={activeIndicatorVariants}
                      initial="initial"
                      animate="animate"
                      exit="exit"
                      layoutId="desktopActiveIndicator"
                      style={{
                        background: 'var(--gradient-primary)',
                        boxShadow: '0 8px 32px rgba(139, 92, 246, 0.4)',
                      }}
                    />
                  )}

                  {/* Icon */}
                  <motion.div
                    className={`relative z-10 text-responsive-lg ${
                      activeSection === item.id
                        ? 'text-inverse'
                        : 'text-quaternary hover:text-primary'
                    }`}
                    animate={{
                      scale: activeSection === item.id ? 1.1 : 1,
                      rotate: activeSection === item.id ? [0, -10, 10, 0] : 0
                    }}
                    transition={{ duration: 0.3 }}
                  >
                    {item.icon}
                  </motion.div>

                  {/* Label */}
                  <motion.span
                    className={`relative z-10 font-medium ${
                      activeSection === item.id
                        ? 'text-inverse'
                        : 'text-quaternary hover:text-primary'
                    }`}
                    animate={{
                      opacity: activeSection === item.id ? 1 : 0.7,
                      y: activeSection === item.id ? 0 : 1
                    }}
                    transition={{ duration: 0.3 }}
                    style={{ fontSize: '10px' }}
                  >
                    {item.label}
                  </motion.span>

                </motion.button>
              ))}
            </div>
          </div>


        </motion.div>
      )}
    </AnimatePresence>
  )
}

export default DesktopNavigation
