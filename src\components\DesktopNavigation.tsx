import { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'

const DesktopNavigation = () => {
  const [activeSection, setActiveSection] = useState('hero')
  const [isVisible, setIsVisible] = useState(false)

  const navItems = [
    { id: 'hero', icon: '🏠', label: 'Home' },
    { id: 'about', icon: '👨‍💻', label: 'About' },
    { id: 'projects', icon: '💼', label: 'Projects' },
    { id: 'github-repos', icon: '🐙', label: 'Repos' },
    { id: 'skills', icon: '🛠️', label: 'Skills' },
    { id: 'education', icon: '🎓', label: 'Experience' },
    { id: 'testimonials', icon: '💬', label: 'Reviews' },
    { id: 'blog', icon: '📝', label: 'Blog' },
    { id: 'contact', icon: '📧', label: 'Contact' }
  ]

  useEffect(() => {
    const handleScroll = () => {
      // Show navigation after scrolling down a bit
      setIsVisible(window.scrollY > 100)

      // Update active section based on scroll position
      const sections = navItems.map(item => item.id)
      const scrollPosition = window.scrollY + 100

      for (const sectionId of sections) {
        const element = sectionId === 'hero' 
          ? document.documentElement 
          : document.getElementById(sectionId)
        
        if (element) {
          const { offsetTop, offsetHeight } = element
          if (scrollPosition >= offsetTop && scrollPosition < offsetTop + offsetHeight) {
            setActiveSection(sectionId)
            break
          }
        }
      }
    }

    window.addEventListener('scroll', handleScroll)
    return () => window.removeEventListener('scroll', handleScroll)
  }, [])

  const scrollToSection = (sectionId: string) => {
    const element = sectionId === 'hero' 
      ? document.documentElement 
      : document.getElementById(sectionId)
    
    if (element) {
      element.scrollIntoView({ behavior: 'smooth' })
      setActiveSection(sectionId)
    }
  }

  const containerVariants = {
    hidden: { 
      x: 100, 
      opacity: 0,
      scale: 0.8
    },
    visible: { 
      x: 0, 
      opacity: 1,
      scale: 1,
      transition: {
        type: "spring" as const,
        stiffness: 300,
        damping: 30,
        staggerChildren: 0.05
      }
    },
    exit: {
      x: 100,
      opacity: 0,
      scale: 0.8,
      transition: {
        duration: 0.3
      }
    }
  }

  const itemVariants = {
    hidden: { scale: 0, opacity: 0 },
    visible: { 
      scale: 1, 
      opacity: 1,
      transition: {
        type: "spring" as const,
        stiffness: 400,
        damping: 25
      }
    }
  }

  const activeIndicatorVariants = {
    initial: { scale: 0 },
    animate: { scale: 1 },
    exit: { scale: 0 }
  }

  return (
    <AnimatePresence>
      {isVisible && (
        <motion.div
          className="fixed right-6 top-1/2 transform -translate-y-1/2 hidden md:block"
          variants={containerVariants}
          initial="hidden"
          animate="visible"
          exit="exit"
          style={{
            zIndex: 'var(--z-fixed)',
          }}
        >
          <div 
            className="backdrop-blur-lg shadow-custom-2xl"
            style={{
              backgroundColor: 'var(--bg-overlay)',
              border: '1px solid var(--border-secondary)',
              borderRadius: 'var(--radius-2xl)',
              padding: 'var(--space-md)',
            }}
          >
            <div 
              className="flex flex-col"
              style={{ gap: 'var(--space-xs)' }}
            >
              {navItems.map((item) => (
                <motion.button
                  key={item.id}
                  onClick={() => scrollToSection(item.id)}
                  className="relative rounded-xl transition-normal touch-target"
                  variants={itemVariants}
                  whileHover={{ scale: 1.1 }}
                  whileTap={{ scale: 0.95 }}
                  style={{
                    padding: 'var(--space-md)',
                    minWidth: '48px',
                    minHeight: '48px',
                  }}
                >
                  {/* Active indicator */}
                  {activeSection === item.id && (
                    <motion.div
                      className="absolute inset-0 rounded-xl"
                      variants={activeIndicatorVariants}
                      initial="initial"
                      animate="animate"
                      exit="exit"
                      layoutId="desktopActiveIndicator"
                      style={{
                        background: 'var(--gradient-primary)',
                      }}
                    />
                  )}
                  
                  {/* Icon */}
                  <motion.div
                    className={`relative z-10 text-responsive-lg ${
                      activeSection === item.id 
                        ? 'text-inverse' 
                        : 'text-quaternary'
                    }`}
                    animate={{
                      scale: activeSection === item.id ? 1.1 : 1,
                      rotate: activeSection === item.id ? [0, -10, 10, 0] : 0
                    }}
                    transition={{ duration: 0.3 }}
                  >
                    {item.icon}
                  </motion.div>

                  {/* Tooltip */}
                  <motion.div
                    className="absolute right-full mr-3 top-1/2 transform -translate-y-1/2 opacity-0 pointer-events-none whitespace-nowrap"
                    whileHover={{ opacity: 1, x: -5 }}
                    transition={{ duration: 0.2 }}
                    style={{
                      backgroundColor: 'var(--bg-card)',
                      color: 'var(--text-primary)',
                      fontSize: 'var(--text-xs)',
                      padding: 'var(--space-sm) var(--space-md)',
                      borderRadius: 'var(--radius-lg)',
                      border: '1px solid var(--border-primary)',
                      boxShadow: 'var(--shadow-lg)',
                    }}
                  >
                    {item.label}
                    <div 
                      className="absolute left-full top-1/2 transform -translate-y-1/2"
                      style={{
                        width: 0,
                        height: 0,
                        borderTop: '4px solid transparent',
                        borderBottom: '4px solid transparent',
                        borderLeft: '4px solid var(--border-primary)',
                      }}
                    />
                  </motion.div>
                </motion.button>
              ))}
            </div>
          </div>

          {/* Floating particles effect */}
          <div className="absolute inset-0 pointer-events-none">
            {[...Array(3)].map((_, i) => (
              <motion.div
                key={i}
                className="absolute rounded-full"
                style={{
                  width: '4px',
                  height: '4px',
                  backgroundColor: 'var(--color-primary)',
                  right: `${10 + i * 15}%`,
                  top: `${20 + i * 30}%`,
                }}
                animate={{
                  x: [5, 15, 5],
                  opacity: [0.3, 0.8, 0.3],
                  scale: [0.5, 1, 0.5]
                }}
                transition={{
                  duration: 2 + i * 0.5,
                  repeat: Infinity,
                  delay: i * 0.3
                }}
              />
            ))}
          </div>
        </motion.div>
      )}
    </AnimatePresence>
  )
}

export default DesktopNavigation
