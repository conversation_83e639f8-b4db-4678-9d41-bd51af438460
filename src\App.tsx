import Hero from './components/Hero'
import About from './components/About'
import Projects from './components/Projects'
import Skills from './components/Skills'
import Education from './components/Education'
import Testimonials from './components/Testimonials'
import Blog from './components/Blog'
import GitHubRepos from './components/GitHubRepos'
import Contact from './components/Contact'
import Footer from './components/Footer'
import MobileNavigation from './components/MobileNavigation'
import DesktopNavigation from './components/DesktopNavigation'
import ThemeToggle from './components/ThemeToggle'

function App() {
  return (
    <div className="min-h-screen bg-white dark:bg-gray-900 text-gray-900 dark:text-white transition-colors duration-300">
      <ThemeToggle />
      <Hero />
      <About />
      <Projects />
      <GitHubRepos />
      <Skills />
      <Education />
      <Testimonials />
      <Blog />
      <Contact />
      <Footer />
      <MobileNavigation />
      <DesktopNavigation />
    </div>
  )
}

export default App
