import React from 'react'
import { motion } from 'framer-motion'

const Footer = () => {
  const currentYear = new Date().getFullYear()

  const quickLinks = [
    { name: "About", href: "#about" },
    { name: "Projects", href: "#projects" },
    { name: "Skills", href: "#skills" },
    { name: "Education", href: "#education" },
    { name: "Contact", href: "#contact" }
  ]

  const socialLinks = [
    { name: "GitHub", href: "https://github.com/syedshaheerabbas", icon: "🐙" },
    { name: "LinkedIn", href: "https://www.linkedin.com/in/sy-shaheer-abbas", icon: "💼" },
    { name: "Email", href: "mailto:<EMAIL>", icon: "📧" }
  ]

  const scrollToTop = () => {
    window.scrollTo({ top: 0, behavior: 'smooth' })
  }

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        delayChildren: 0.3,
        staggerChildren: 0.1
      }
    }
  }

  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        duration: 0.5
      }
    }
  }

  return (
    <footer className="bg-secondary" style={{ borderTop: '1px solid var(--border-primary)' }}>
      <div className="container-responsive" style={{ padding: 'var(--space-3xl) var(--space-md)' }}>
        <motion.div
          className="grid md:grid-cols-4 gap-8"
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true }}
          variants={containerVariants}
        >
          {/* Brand Section */}
          <motion.div
            className="md:col-span-2"
            variants={itemVariants}
          >
            <motion.h3
              className="text-2xl font-bold text-white mb-4"
              whileHover={{ scale: 1.05 }}
            >
              <span className="bg-gradient-to-r from-purple-400 to-pink-400 bg-clip-text text-transparent">
                Syed Shaheer Abbas
              </span>
            </motion.h3>
            <p className="text-gray-400 mb-4 max-w-md">
              Full-Stack Developer passionate about creating scalable web applications
              using MERN stack and .NET technologies.
            </p>
            <div className="flex space-x-4">
              {socialLinks.map((link, index) => (
                <motion.a
                  key={index}
                  href={link.href}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-gray-400 hover:text-purple-400 transition-colors duration-300"
                  whileHover={{ scale: 1.2 }}
                  whileTap={{ scale: 0.9 }}
                  title={link.name}
                >
                  <span className="text-xl">{link.icon}</span>
                </motion.a>
              ))}
            </div>
          </motion.div>

          {/* Quick Links */}
          <motion.div variants={itemVariants}>
            <h4 className="text-lg font-semibold text-white mb-4">Quick Links</h4>
            <ul className="space-y-2">
              {quickLinks.map((link, index) => (
                <li key={index}>
                  <motion.a
                    href={link.href}
                    className="text-gray-400 hover:text-purple-400 transition-colors duration-300"
                    whileHover={{ x: 5 }}
                  >
                    {link.name}
                  </motion.a>
                </li>
              ))}
            </ul>
          </motion.div>

          {/* Contact Info */}
          <motion.div variants={itemVariants}>
            <h4 className="text-lg font-semibold text-white mb-4">Get In Touch</h4>
            <div className="space-y-2">
              <p className="text-gray-400">
                📧 <EMAIL>
              </p>
              <p className="text-gray-400">
                📱 +92 348 4587063
              </p>
              <p className="text-gray-400">
                📍 Lahore, Pakistan
              </p>
              <motion.button
                onClick={scrollToTop}
                className="mt-4 px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors duration-300"
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                Back to Top ↑
              </motion.button>
            </div>
          </motion.div>
        </motion.div>

        {/* Bottom Section */}
        <motion.div
          className="border-t border-gray-700 mt-8 pt-8 flex flex-col md:flex-row justify-between items-center"
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true }}
          variants={containerVariants}
        >
          <motion.p
            className="text-gray-400 text-sm mb-4 md:mb-0"
            variants={itemVariants}
          >
            © {currentYear} Syed Shaheer Abbas. All rights reserved.
          </motion.p>
          
          <motion.div
            className="flex items-center space-x-4 text-sm text-gray-400"
            variants={itemVariants}
          >
            <span>Built with</span>
            <motion.span
              className="text-purple-400"
              animate={{ scale: [1, 1.2, 1] }}
              transition={{ duration: 2, repeat: Infinity }}
            >
              ❤️
            </motion.span>
            <span>using React & Framer Motion</span>
            <motion.a
              href="https://github.com/syedshaheerabbas/portfolio"
              target="_blank"
              rel="noopener noreferrer"
              className="text-purple-400 hover:text-purple-300 transition-colors duration-300"
              whileHover={{ scale: 1.05 }}
            >
              View Source
            </motion.a>
          </motion.div>
        </motion.div>
      </div>
    </footer>
  )
}

export default Footer
