import { motion } from 'framer-motion'

const Footer = () => {
  const currentYear = new Date().getFullYear()



  const socialLinks = [
    { name: "GitHub", href: "https://github.com/syedshaheerabbas", icon: "🐙" },
    { name: "LinkedIn", href: "https://www.linkedin.com/in/sy-shaheer-abbas", icon: "💼" },
    { name: "Email", href: "mailto:<EMAIL>", icon: "📧" },
    { name: "Phone", href: "tel:+923484587063", icon: "📱" }
  ]

  const quickLinks = [
    { name: "About", href: "#about" },
    { name: "Projects", href: "#projects" },
    { name: "Skills", href: "#skills" },
    { name: "Experience", href: "#education" },
    { name: "Contact", href: "#contact" }
  ]

  const scrollToTop = () => {
    window.scrollTo({ top: 0, behavior: 'smooth' })
  }

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        delayChildren: 0.3,
        staggerChildren: 0.1
      }
    }
  }

  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        duration: 0.5
      }
    }
  }

  return (
    <footer className="bg-secondary" style={{ borderTop: '1px solid var(--border-primary)' }}>
      <div className="container-responsive" style={{ padding: 'var(--space-3xl) var(--space-md)' }}>
        <motion.div
          className="grid-responsive-3"
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true }}
          variants={containerVariants}
        >
          {/* Brand Section */}
          <motion.div
            variants={itemVariants}
          >
            <motion.h3
              className="text-responsive-2xl font-bold text-primary"
              whileHover={{ scale: 1.05 }}
              style={{ marginBottom: 'var(--space-md)' }}
            >
              <span
                className="bg-clip-text text-transparent"
                style={{
                  background: 'var(--gradient-primary)',
                  WebkitBackgroundClip: 'text',
                  WebkitTextFillColor: 'transparent',
                }}
              >
                Syed Shaheer Abbas
              </span>
            </motion.h3>
            <p
              className="text-secondary max-w-md text-responsive-base leading-relaxed"
              style={{ marginBottom: 'var(--space-md)' }}
            >
              Full-Stack Developer passionate about creating scalable web applications
              using MERN stack and .NET technologies.
            </p>
            <div
              className="flex flex-wrap"
              style={{ gap: 'var(--space-md)' }}
            >
              {socialLinks.map((link, index) => (
                <motion.a
                  key={index}
                  href={link.href}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="card-base hover:bg-hover transition-normal touch-target"
                  whileHover={{ scale: 1.1, rotate: 5 }}
                  whileTap={{ scale: 0.9 }}
                  title={link.name}
                  style={{
                    width: '48px',
                    height: '48px',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    textDecoration: 'none',
                    borderRadius: 'var(--radius-full)',
                  }}
                >
                  <span className="text-responsive-lg">{link.icon}</span>
                </motion.a>
              ))}
            </div>
          </motion.div>

          {/* Quick Links */}
          <motion.div variants={itemVariants}>
            <h4
              className="text-responsive-lg font-semibold text-primary"
              style={{ marginBottom: 'var(--space-md)' }}
            >
              Quick Links
            </h4>
            <ul style={{ display: 'flex', flexDirection: 'column', gap: 'var(--space-sm)' }}>
              {quickLinks.map((link, index) => (
                <li key={index}>
                  <motion.a
                    href={link.href}
                    className="text-secondary hover:text-brand-primary transition-normal text-responsive-base"
                    whileHover={{ x: 5 }}
                    style={{ textDecoration: 'none' }}
                  >
                    {link.name}
                  </motion.a>
                </li>
              ))}
            </ul>
          </motion.div>

          {/* Contact Info */}
          <motion.div variants={itemVariants}>
            <h4
              className="text-responsive-lg font-semibold text-primary"
              style={{ marginBottom: 'var(--space-md)' }}
            >
              Get In Touch
            </h4>
            <div style={{ display: 'flex', flexDirection: 'column', gap: 'var(--space-sm)' }}>
              <motion.a
                href="mailto:<EMAIL>"
                className="text-secondary hover:text-brand-primary transition-normal text-responsive-base"
                whileHover={{ x: 5 }}
                style={{ textDecoration: 'none', display: 'flex', alignItems: 'center', gap: 'var(--space-sm)' }}
              >
                <span>📧</span>
                <span><EMAIL></span>
              </motion.a>
              <motion.a
                href="tel:+923484587063"
                className="text-secondary hover:text-brand-primary transition-normal text-responsive-base"
                whileHover={{ x: 5 }}
                style={{ textDecoration: 'none', display: 'flex', alignItems: 'center', gap: 'var(--space-sm)' }}
              >
                <span>📱</span>
                <span>+92 348 4587063</span>
              </motion.a>
              <div
                className="text-secondary text-responsive-base"
                style={{ display: 'flex', alignItems: 'center', gap: 'var(--space-sm)' }}
              >
                <span>📍</span>
                <span>Lahore, Pakistan</span>
              </div>
              <motion.button
                onClick={scrollToTop}
                className="btn-primary touch-target"
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                style={{
                  marginTop: 'var(--space-md)',
                  alignSelf: 'flex-start',
                }}
              >
                Back to Top ↑
              </motion.button>
            </div>
          </motion.div>
        </motion.div>

        {/* Bottom Section */}
        <motion.div
          className="flex flex-col md:flex-row justify-between items-center"
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true }}
          variants={containerVariants}
          style={{
            borderTop: '1px solid var(--border-primary)',
            marginTop: 'var(--space-2xl)',
            paddingTop: 'var(--space-2xl)',
            gap: 'var(--space-md)',
          }}
        >
          <motion.p
            className="text-secondary text-responsive-sm"
            variants={itemVariants}
          >
            © {currentYear} Syed Shaheer Abbas. All rights reserved.
          </motion.p>

          <motion.div
            className="flex items-center text-responsive-sm text-quaternary"
            variants={itemVariants}
            style={{ gap: 'var(--space-sm)' }}
          >
            <span>Built with</span>
            <motion.span
              className="text-brand-primary"
              animate={{ scale: [1, 1.2, 1] }}
              transition={{ duration: 2, repeat: Infinity }}
            >
              ❤️
            </motion.span>
            <span>using React & Framer Motion</span>
            <motion.a
              href="https://github.com/syedshaheerabbas/portfolio"
              target="_blank"
              rel="noopener noreferrer"
              className="text-brand-primary hover:text-brand-secondary transition-normal"
              whileHover={{ scale: 1.05 }}
              style={{ textDecoration: 'none' }}
            >
              View Source
            </motion.a>
          </motion.div>
        </motion.div>
      </div>
    </footer>
  )
}

export default Footer
