import { motion } from 'framer-motion'

const About = () => {
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        delayChildren: 0.3,
        staggerChildren: 0.2
      }
    }
  }

  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        duration: 0.5
      }
    }
  }

  return (
    <section id="about" className="bg-secondary" style={{ padding: 'var(--space-5xl) 0' }}>
      <div className="container-responsive">
        <motion.div
          className="text-center"
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true }}
          variants={containerVariants}
          style={{ marginBottom: 'var(--space-4xl)' }}
        >
          <motion.h2
            className="text-responsive-5xl font-bold text-primary"
            variants={itemVariants}
            style={{ marginBottom: 'var(--space-md)' }}
          >
            About Me
          </motion.h2>
          <motion.div
            className="mx-auto"
            variants={itemVariants}
            style={{
              width: '96px',
              height: '4px',
              background: 'var(--gradient-primary)',
              borderRadius: 'var(--radius-full)',
            }}
          />
        </motion.div>

        <div className="grid-responsive-2 items-center">
          <motion.div
            className="flex justify-center md:justify-start"
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true }}
            variants={containerVariants}
          >
            <motion.div
              className="relative"
              variants={itemVariants}
            >
              <motion.div
                className="relative group mx-auto md:mx-0"
                whileHover={{ scale: 1.08 }}
                transition={{ duration: 0.4, type: "spring", stiffness: 300 }}
                style={{
                  width: 'clamp(220px, 32vw, 280px)',
                  height: 'clamp(220px, 32vw, 280px)',
                }}
              >
                {/* Glow effect background */}
                <motion.div
                  className="absolute inset-0 rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-500"
                  style={{
                    background: 'var(--gradient-primary)',
                    filter: 'blur(25px)',
                    transform: 'scale(1.3)',
                  }}
                  animate={{
                    scale: [1.3, 1.4, 1.3],
                    opacity: [0.2, 0.4, 0.2],
                  }}
                  transition={{
                    duration: 3,
                    repeat: Infinity,
                    ease: "easeInOut"
                  }}
                />

                {/* Outer rotating ring */}
                <motion.div
                  className="absolute inset-0 rounded-full"
                  animate={{ rotate: 360 }}
                  transition={{ duration: 20, repeat: Infinity, ease: "linear" }}
                  style={{
                    border: '3px solid transparent',
                    background: 'var(--gradient-primary)',
                    backgroundClip: 'padding-box',
                  }}
                />

                {/* Main profile container */}
                <motion.div
                  className="absolute inset-1 rounded-full"
                  style={{
                    background: 'var(--gradient-primary)',
                    padding: '4px',
                  }}
                  whileHover={{
                    boxShadow: '0 0 50px rgba(139, 92, 246, 0.7)',
                  }}
                  transition={{ duration: 0.3 }}
                >
                  {/* Profile content */}
                  <div
                    className="relative w-full h-full rounded-full overflow-hidden"
                    style={{
                      background: 'var(--bg-card)',
                      border: '2px solid var(--bg-card)',
                    }}
                  >
                    {/* Profile Image/Content */}
                    <motion.div
                      className="absolute inset-0 flex flex-col items-center justify-center"
                      style={{
                        background: 'var(--gradient-secondary)',
                        backgroundSize: 'cover',
                        backgroundPosition: 'center',
                      }}
                      whileHover={{
                        scale: 1.05,
                      }}
                      transition={{ duration: 0.4 }}
                    >
                      {/* Main initials */}
                      <motion.div
                        className="flex flex-col items-center justify-center text-inverse font-bold"
                        style={{
                          fontSize: 'clamp(2.2rem, 5.5vw, 3.2rem)',
                          color: 'var(--text-inverse)',
                          textShadow: '0 2px 15px rgba(0, 0, 0, 0.4)',
                        }}
                      >
                        <motion.span
                          animate={{
                            textShadow: [
                              '0 2px 15px rgba(0, 0, 0, 0.4)',
                              '0 4px 25px rgba(139, 92, 246, 0.6)',
                              '0 2px 15px rgba(0, 0, 0, 0.4)',
                            ],
                          }}
                          transition={{
                            duration: 2.5,
                            repeat: Infinity,
                            ease: "easeInOut"
                          }}
                        >
                          SSA
                        </motion.span>

                        {/* Subtitle */}
                        <motion.div
                          className="text-xs mt-1 opacity-90 font-medium"
                          style={{
                            fontSize: 'clamp(0.6rem, 1.2vw, 0.75rem)',
                            letterSpacing: '0.15em',
                            textShadow: '0 1px 5px rgba(0, 0, 0, 0.3)',
                          }}
                          initial={{ opacity: 0, y: 10 }}
                          animate={{ opacity: 0.9, y: 0 }}
                          transition={{ delay: 0.5 }}
                        >
                          FULL STACK DEV
                        </motion.div>
                      </motion.div>

                      {/* Hover overlay */}
                      <motion.div
                        className="absolute inset-0 bg-black opacity-0 group-hover:opacity-10 transition-opacity duration-300"
                        style={{ borderRadius: 'inherit' }}
                      />
                    </motion.div>

                    {/* Floating particles */}
                    {[...Array(6)].map((_, i) => (
                      <motion.div
                        key={i}
                        className="absolute rounded-full pointer-events-none"
                        style={{
                          width: '3px',
                          height: '3px',
                          backgroundColor: 'rgba(139, 92, 246, 0.7)',
                          left: `${15 + (i * 12)}%`,
                          top: `${10 + ((i % 3) * 30)}%`,
                        }}
                        animate={{
                          y: [-8, -20, -8],
                          opacity: [0.3, 0.8, 0.3],
                          scale: [0.5, 1, 0.5],
                        }}
                        transition={{
                          duration: 2.5 + i * 0.2,
                          repeat: Infinity,
                          delay: i * 0.3,
                        }}
                      />
                    ))}
                  </div>
                </motion.div>

                {/* Status indicator */}
                <motion.div
                  className="absolute bottom-4 right-4 rounded-full z-10"
                  style={{
                    width: '18px',
                    height: '18px',
                    backgroundColor: 'var(--color-success)',
                    border: '3px solid var(--bg-card)',
                    boxShadow: '0 0 15px rgba(16, 185, 129, 0.6)',
                  }}
                  animate={{
                    scale: [1, 1.3, 1],
                    boxShadow: [
                      '0 0 15px rgba(16, 185, 129, 0.6)',
                      '0 0 25px rgba(16, 185, 129, 0.9)',
                      '0 0 15px rgba(16, 185, 129, 0.6)',
                    ],
                  }}
                  transition={{
                    duration: 2,
                    repeat: Infinity,
                    ease: "easeInOut"
                  }}
                />
              </motion.div>
            </motion.div>
          </motion.div>

          <motion.div
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true }}
            variants={containerVariants}
            style={{
              display: 'flex',
              flexDirection: 'column',
              gap: 'var(--space-lg)',
            }}
          >
            <motion.h3
              className="text-responsive-2xl font-semibold text-primary"
              variants={itemVariants}
            >
              Hello! I'm Syed Shaheer Abbas
            </motion.h3>

            <motion.p
              className="text-secondary leading-relaxed text-responsive-base"
              variants={itemVariants}
            >
              Motivated and skilled full-stack developer with hands-on experience in MERN and .NET.
              Passionate about building efficient, scalable, and clean web applications with strong
              cross-platform logic.
            </motion.p>

            <motion.div
              variants={itemVariants}
              style={{
                display: 'flex',
                flexDirection: 'column',
                gap: 'var(--space-md)',
              }}
            >
              <div
                className="flex items-center text-responsive-sm"
                style={{ gap: 'var(--space-sm)' }}
              >
                <span className="text-brand-primary">🎓</span>
                <span className="text-secondary">Bachelors in Computer Science (2022–2026), NUML</span>
              </div>
              <div
                className="flex items-center text-responsive-sm"
                style={{ gap: 'var(--space-sm)' }}
              >
                <span className="text-brand-primary">💡</span>
                <span className="text-secondary">Fluent in English, Urdu, Hindi</span>
              </div>
              <div
                className="flex items-center text-responsive-sm"
                style={{ gap: 'var(--space-sm)' }}
              >
                <span className="text-brand-primary">💻</span>
                <span className="text-secondary">Loves both frontend & backend logic</span>
              </div>
              <div
                className="flex items-center text-responsive-sm"
                style={{ gap: 'var(--space-sm)' }}
              >
                <span className="text-brand-primary">👨‍🏫</span>
                <span className="text-secondary">Hosted Hackforge & Chess Tournament @ ACM</span>
              </div>
            </motion.div>

            <motion.div
              className="grid grid-cols-1 sm:grid-cols-2 touch-target"
              variants={itemVariants}
              style={{
                gap: 'var(--space-md)',
                marginTop: 'var(--space-lg)',
              }}
            >
              <div
                className="card-base"
                style={{ padding: 'var(--space-md)' }}
              >
                <h4 className="text-brand-primary font-semibold text-responsive-sm" style={{ marginBottom: 'var(--space-sm)' }}>
                  Education
                </h4>
                <p className="text-secondary text-responsive-xs">BS Computer Science, NUML</p>
              </div>
              <div
                className="card-base"
                style={{ padding: 'var(--space-md)' }}
              >
                <h4 className="text-brand-primary font-semibold text-responsive-sm" style={{ marginBottom: 'var(--space-sm)' }}>
                  Focus
                </h4>
                <p className="text-secondary text-responsive-xs">MERN & .NET Stack</p>
              </div>
              <div
                className="card-base"
                style={{ padding: 'var(--space-md)' }}
              >
                <h4 className="text-brand-primary font-semibold text-responsive-sm" style={{ marginBottom: 'var(--space-sm)' }}>
                  Location
                </h4>
                <p className="text-secondary text-responsive-xs">Lahore, Pakistan</p>
              </div>
              <div
                className="card-base"
                style={{ padding: 'var(--space-md)' }}
              >
                <h4 className="text-brand-primary font-semibold text-responsive-sm" style={{ marginBottom: 'var(--space-sm)' }}>
                  Goal
                </h4>
                <p className="text-secondary text-responsive-xs">Scalable Solutions</p>
              </div>
            </motion.div>
          </motion.div>
        </div>
      </div>
    </section>
  )
}

export default About
