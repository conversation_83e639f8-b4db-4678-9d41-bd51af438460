import { motion } from 'framer-motion'

const About = () => {
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        delayChildren: 0.3,
        staggerChildren: 0.2
      }
    }
  }

  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        duration: 0.5
      }
    }
  }

  return (
    <section id="about" className="bg-secondary" style={{ padding: 'var(--space-5xl) 0' }}>
      <div className="container-responsive">
        <motion.div
          className="text-center"
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true }}
          variants={containerVariants}
          style={{ marginBottom: 'var(--space-4xl)' }}
        >
          <motion.h2
            className="text-responsive-5xl font-bold text-primary"
            variants={itemVariants}
            style={{ marginBottom: 'var(--space-md)' }}
          >
            About Me
          </motion.h2>
          <motion.div
            className="mx-auto"
            variants={itemVariants}
            style={{
              width: '96px',
              height: '4px',
              background: 'var(--gradient-primary)',
              borderRadius: 'var(--radius-full)',
            }}
          />
        </motion.div>

        <div className="grid-responsive-2 items-center">
          <motion.div
            className="flex justify-center md:justify-start"
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true }}
            variants={containerVariants}
          >
            <motion.div
              className="relative"
              variants={itemVariants}
            >
              <div
                className="relative mx-auto md:mx-0"
                style={{
                  width: 'clamp(200px, 30vw, 256px)',
                  height: 'clamp(200px, 30vw, 256px)',
                }}
              >
                <div
                  className="w-full h-full rounded-full p-1"
                  style={{
                    background: 'var(--gradient-primary)',
                  }}
                >
                  <div
                    className="w-full h-full bg-secondary rounded-full flex items-center justify-center"
                  >
                    <div
                      className="rounded-full flex items-center justify-center text-primary font-bold"
                      style={{
                        width: 'calc(100% - 16px)',
                        height: 'calc(100% - 16px)',
                        background: 'var(--gradient-secondary)',
                        fontSize: 'clamp(2rem, 5vw, 3rem)',
                        color: 'var(--text-inverse)',
                      }}
                    >
                      SSA
                    </div>
                  </div>
                </div>
                <motion.div
                  className="absolute inset-0 rounded-full"
                  animate={{ rotate: 360 }}
                  transition={{ duration: 20, repeat: Infinity, ease: "linear" }}
                  style={{
                    border: '4px solid var(--color-primary)',
                  }}
                />
              </div>
            </motion.div>
          </motion.div>

          <motion.div
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true }}
            variants={containerVariants}
            style={{
              display: 'flex',
              flexDirection: 'column',
              gap: 'var(--space-lg)',
            }}
          >
            <motion.h3
              className="text-responsive-2xl font-semibold text-primary"
              variants={itemVariants}
            >
              Hello! I'm Syed Shaheer Abbas
            </motion.h3>

            <motion.p
              className="text-secondary leading-relaxed text-responsive-base"
              variants={itemVariants}
            >
              Motivated and skilled full-stack developer with hands-on experience in MERN and .NET.
              Passionate about building efficient, scalable, and clean web applications with strong
              cross-platform logic.
            </motion.p>

            <motion.div
              variants={itemVariants}
              style={{
                display: 'flex',
                flexDirection: 'column',
                gap: 'var(--space-md)',
              }}
            >
              <div
                className="flex items-center text-responsive-sm"
                style={{ gap: 'var(--space-sm)' }}
              >
                <span className="text-brand-primary">🎓</span>
                <span className="text-secondary">Bachelors in Computer Science (2022–2026), NUML</span>
              </div>
              <div
                className="flex items-center text-responsive-sm"
                style={{ gap: 'var(--space-sm)' }}
              >
                <span className="text-brand-primary">💡</span>
                <span className="text-secondary">Fluent in English, Urdu, Hindi</span>
              </div>
              <div
                className="flex items-center text-responsive-sm"
                style={{ gap: 'var(--space-sm)' }}
              >
                <span className="text-brand-primary">💻</span>
                <span className="text-secondary">Loves both frontend & backend logic</span>
              </div>
              <div
                className="flex items-center text-responsive-sm"
                style={{ gap: 'var(--space-sm)' }}
              >
                <span className="text-brand-primary">👨‍🏫</span>
                <span className="text-secondary">Hosted Hackforge & Chess Tournament @ ACM</span>
              </div>
            </motion.div>

            <motion.div
              className="grid grid-cols-1 sm:grid-cols-2 touch-target"
              variants={itemVariants}
              style={{
                gap: 'var(--space-md)',
                marginTop: 'var(--space-lg)',
              }}
            >
              <div
                className="card-base"
                style={{ padding: 'var(--space-md)' }}
              >
                <h4 className="text-brand-primary font-semibold text-responsive-sm" style={{ marginBottom: 'var(--space-sm)' }}>
                  Education
                </h4>
                <p className="text-secondary text-responsive-xs">BS Computer Science, NUML</p>
              </div>
              <div
                className="card-base"
                style={{ padding: 'var(--space-md)' }}
              >
                <h4 className="text-brand-primary font-semibold text-responsive-sm" style={{ marginBottom: 'var(--space-sm)' }}>
                  Focus
                </h4>
                <p className="text-secondary text-responsive-xs">MERN & .NET Stack</p>
              </div>
              <div
                className="card-base"
                style={{ padding: 'var(--space-md)' }}
              >
                <h4 className="text-brand-primary font-semibold text-responsive-sm" style={{ marginBottom: 'var(--space-sm)' }}>
                  Location
                </h4>
                <p className="text-secondary text-responsive-xs">Lahore, Pakistan</p>
              </div>
              <div
                className="card-base"
                style={{ padding: 'var(--space-md)' }}
              >
                <h4 className="text-brand-primary font-semibold text-responsive-sm" style={{ marginBottom: 'var(--space-sm)' }}>
                  Goal
                </h4>
                <p className="text-secondary text-responsive-xs">Scalable Solutions</p>
              </div>
            </motion.div>
          </motion.div>
        </div>
      </div>
    </section>
  )
}

export default About
