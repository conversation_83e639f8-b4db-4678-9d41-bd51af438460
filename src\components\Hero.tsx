import React from 'react'
import { motion } from 'framer-motion'

const Hero = () => {
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        delayChildren: 0.3,
        staggerChildren: 0.2
      }
    }
  }

  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        duration: 0.5
      }
    }
  }

  const scrollToProjects = () => {
    const projectsSection = document.getElementById('projects')
    if (projectsSection) {
      projectsSection.scrollIntoView({ behavior: 'smooth' })
    }
  }

  return (
    <section className="min-h-screen flex items-center justify-center bg-gradient-hero relative overflow-hidden">
      {/* Animated background particles */}
      <div className="absolute inset-0">
        {[...Array(50)].map((_, i) => (
          <motion.div
            key={i}
            className="absolute w-1 h-1 rounded-full opacity-20"
            style={{
              left: `${Math.random() * 100}%`,
              top: `${Math.random() * 100}%`,
              backgroundColor: 'var(--text-primary)',
            }}
            animate={{
              y: [0, -30, 0],
              opacity: [0.2, 0.5, 0.2],
            }}
            transition={{
              duration: 3 + Math.random() * 2,
              repeat: Infinity,
              delay: Math.random() * 2,
            }}
          />
        ))}
      </div>

      <motion.div
        className="text-center z-10 container-responsive max-w-4xl"
        variants={containerVariants}
        initial="hidden"
        animate="visible"
      >
        <motion.h1
          className="text-responsive-6xl font-bold text-primary mb-6"
          variants={itemVariants}
          style={{
            lineHeight: '1.1',
            marginBottom: 'var(--space-lg)',
          }}
        >
          Hi, I'm{' '}
          <span
            className="bg-clip-text text-transparent"
            style={{
              background: 'var(--gradient-primary)',
              WebkitBackgroundClip: 'text',
              WebkitTextFillColor: 'transparent',
            }}
          >
            Syed Shaheer Abbas
          </span>
        </motion.h1>

        <motion.h2
          className="text-responsive-xl text-secondary font-medium"
          variants={itemVariants}
          style={{
            marginBottom: 'var(--space-md)',
            lineHeight: '1.4',
          }}
        >
          Full-Stack Developer | MERN & .NET Enthusiast
        </motion.h2>

        <motion.p
          className="text-responsive-lg text-tertiary max-w-2xl mx-auto"
          variants={itemVariants}
          style={{
            marginBottom: 'var(--space-md)',
            lineHeight: '1.6',
          }}
        >
          I turn clean code into scalable solutions.
        </motion.p>

        <motion.p
          className="text-responsive-base text-quaternary"
          variants={itemVariants}
          style={{
            marginBottom: 'var(--space-2xl)',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            gap: 'var(--space-sm)',
          }}
        >
          <span>📍</span>
          <span>Lahore, Pakistan</span>
        </motion.p>

        <motion.div
          className="flex flex-col sm:flex-row justify-center touch-target"
          variants={itemVariants}
          style={{
            gap: 'var(--space-md)',
            marginBottom: 'var(--space-3xl)',
          }}
        >
          <motion.a
            href="/resume.pdf"
            download="Syed_Shaheer_Abbas_Resume.pdf"
            className="btn-primary text-center"
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            style={{
              textDecoration: 'none',
              display: 'inline-block',
              minWidth: '200px',
            }}
          >
            Download Resume
          </motion.a>

          <motion.button
            onClick={scrollToProjects}
            className="btn-secondary"
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            style={{
              minWidth: '200px',
            }}
          >
            View Projects
          </motion.button>
        </motion.div>

        <motion.div
          className="flex flex-col items-center"
          variants={itemVariants}
        >
          <motion.div
            className="rounded-full mx-auto flex justify-center"
            animate={{ y: [0, 10, 0] }}
            transition={{ duration: 2, repeat: Infinity }}
            style={{
              width: '24px',
              height: '40px',
              border: '2px solid var(--text-quaternary)',
              borderRadius: 'var(--radius-full)',
            }}
          >
            <motion.div
              className="rounded-full"
              animate={{ opacity: [1, 0, 1] }}
              transition={{ duration: 2, repeat: Infinity }}
              style={{
                width: '4px',
                height: '12px',
                backgroundColor: 'var(--text-quaternary)',
                borderRadius: 'var(--radius-full)',
                marginTop: '8px',
              }}
            />
          </motion.div>
          <p
            className="text-quaternary text-responsive-sm"
            style={{
              marginTop: 'var(--space-sm)',
            }}
          >
            Scroll down
          </p>
        </motion.div>
      </motion.div>
    </section>
  )
}

export default Hero
