{"name": "portfolio", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview"}, "devDependencies": {"@vitejs/plugin-react": "^4.6.0", "typescript": "~5.8.3", "vite": "^7.0.0"}, "dependencies": {"@tailwindcss/vite": "^4.1.11", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "framer-motion": "^12.20.1", "react": "^19.1.0", "react-dom": "^19.1.0", "tailwindcss": "^4.1.11"}}