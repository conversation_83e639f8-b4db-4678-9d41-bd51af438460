import React from 'react'
import { motion } from 'framer-motion'

const Education = () => {
  const timeline = [
    {
      id: 1,
      type: "education",
      title: "Bachelor of Science in Computer Science",
      institution: "National University of Modern Languages (NUML)",
      period: "2022 - 2026",
      description: "Currently pursuing BS Computer Science with focus on software engineering, web development, and database management. Active member of ACM chapter.",
      achievements: ["Third place @ Hackforge", "Hosted Hackforge @ NUML (ACM)", "Hosted Chess Tournament @ NUML (ACM)"]
    },
    {
      id: 2,
      type: "experience",
      title: "Product Debriefing Coordinator",
      institution: "Postex",
      period: "Oct 2024 - May 2025",
      description: "Debriefed logistics systems, documented internal protocols, and improved QA feedback loops. Enhanced operational efficiency through systematic documentation.",
      achievements: ["Improved QA feedback loops", "Documented internal protocols", "Enhanced logistics systems"]
    },
    {
      id: 3,
      type: "experience",
      title: "Data Analyst",
      institution: "Hired Support",
      period: "Jun 2022 - Sep 2023",
      description: "Analyzed audio quality, enhanced training materials, and collaborated with QA teams. Contributed to improving data accuracy and training processes.",
      achievements: ["Enhanced training materials", "Improved data accuracy", "Collaborated with QA teams"]
    },
    {
      id: 4,
      type: "education",
      title: "Intermediate in Computer Science",
      institution: "Islamia College",
      period: "2020 - 2022",
      description: "Completed intermediate education with focus on computer science fundamentals. Achieved 70% marks while building foundation in programming concepts.",
      achievements: ["70% marks", "Computer Science focus", "Programming foundation"]
    }
  ]

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        delayChildren: 0.3,
        staggerChildren: 0.2
      }
    }
  }

  const itemVariants = {
    hidden: { x: -50, opacity: 0 },
    visible: {
      x: 0,
      opacity: 1,
      transition: {
        duration: 0.6
      }
    }
  }

  const timelineVariants = {
    hidden: { scaleY: 0 },
    visible: {
      scaleY: 1,
      transition: {
        duration: 1,
        delay: 0.5
      }
    }
  }

  return (
    <section id="education" className="py-20 bg-gray-900">
      <div className="max-w-6xl mx-auto px-4">
        <motion.div
          className="text-center mb-16"
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true }}
          variants={containerVariants}
        >
          <motion.h2
            className="text-4xl md:text-5xl font-bold text-white mb-4"
            variants={itemVariants}
          >
            Education & Experience
          </motion.h2>
          <motion.div
            className="w-24 h-1 bg-gradient-to-r from-purple-500 to-pink-500 mx-auto mb-4"
            variants={itemVariants}
          />
          <motion.p
            className="text-gray-400 max-w-2xl mx-auto"
            variants={itemVariants}
          >
            My academic journey and professional experiences that have shaped my skills and perspective.
          </motion.p>
        </motion.div>

        <div className="relative">
          {/* Timeline line */}
          <motion.div
            className="absolute left-1/2 transform -translate-x-1/2 w-1 bg-gradient-to-b from-purple-500 to-pink-500 h-full origin-top"
            variants={timelineVariants}
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true }}
          />

          <motion.div
            className="space-y-12"
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true }}
            variants={containerVariants}
          >
            {timeline.map((item, index) => (
              <motion.div
                key={item.id}
                className={`flex items-center ${
                  index % 2 === 0 ? 'flex-row' : 'flex-row-reverse'
                }`}
                variants={itemVariants}
              >
                <div className={`w-1/2 ${index % 2 === 0 ? 'pr-8' : 'pl-8'}`}>
                  <motion.div
                    className="bg-gray-800 p-6 rounded-lg shadow-lg"
                    whileHover={{ scale: 1.02 }}
                    transition={{ duration: 0.2 }}
                  >
                    <div className="flex items-center mb-3">
                      <div className={`w-3 h-3 rounded-full mr-3 ${
                        item.type === 'education' 
                          ? 'bg-purple-500' 
                          : 'bg-pink-500'
                      }`} />
                      <span className="text-gray-400 text-sm font-medium">
                        {item.period}
                      </span>
                    </div>
                    
                    <h3 className="text-xl font-semibold text-white mb-2">
                      {item.title}
                    </h3>
                    
                    <h4 className="text-purple-400 font-medium mb-3">
                      {item.institution}
                    </h4>
                    
                    <p className="text-gray-300 mb-4 leading-relaxed">
                      {item.description}
                    </p>
                    
                    <div className="space-y-2">
                      <h5 className="text-sm font-semibold text-gray-400 uppercase tracking-wide">
                        Key Achievements
                      </h5>
                      <ul className="space-y-1">
                        {item.achievements.map((achievement, achIndex) => (
                          <li key={achIndex} className="text-gray-300 text-sm flex items-center">
                            <span className="w-1.5 h-1.5 bg-purple-400 rounded-full mr-2" />
                            {achievement}
                          </li>
                        ))}
                      </ul>
                    </div>
                  </motion.div>
                </div>

                {/* Timeline dot */}
                <motion.div
                  className="relative z-10"
                  whileHover={{ scale: 1.2 }}
                >
                  <div className={`w-6 h-6 rounded-full border-4 border-gray-900 ${
                    item.type === 'education' 
                      ? 'bg-purple-500' 
                      : 'bg-pink-500'
                  }`} />
                </motion.div>

                <div className="w-1/2" />
              </motion.div>
            ))}
          </motion.div>
        </div>
      </div>
    </section>
  )
}

export default Education
