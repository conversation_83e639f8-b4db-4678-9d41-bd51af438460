import { motion } from 'framer-motion'

const Education = () => {
  const timeline = [
    {
      id: 1,
      type: "education",
      title: "Bachelor of Science in Computer Science",
      institution: "National University of Modern Languages (NUML)",
      period: "2022 - 2026",
      description: "Currently pursuing BS Computer Science with focus on software engineering, web development, and database management. Active member of ACM chapter.",
      achievements: ["Third place @ Hackforge", "Hosted Hackforge @ NUML (ACM)", "Hosted Chess Tournament @ NUML (ACM)"]
    },
    {
      id: 2,
      type: "experience",
      title: "Product Debriefing Coordinator",
      institution: "Postex",
      period: "Oct 2024 - May 2025",
      description: "Debriefed logistics systems, documented internal protocols, and improved QA feedback loops. Enhanced operational efficiency through systematic documentation.",
      achievements: ["Improved QA feedback loops", "Documented internal protocols", "Enhanced logistics systems"]
    },
    {
      id: 3,
      type: "experience",
      title: "Data Analyst",
      institution: "Hired Support",
      period: "Jun 2022 - Sep 2023",
      description: "Analyzed audio quality, enhanced training materials, and collaborated with QA teams. Contributed to improving data accuracy and training processes.",
      achievements: ["Enhanced training materials", "Improved data accuracy", "Collaborated with QA teams"]
    },
    {
      id: 4,
      type: "education",
      title: "Intermediate in Computer Science",
      institution: "Islamia College",
      period: "2020 - 2022",
      description: "Completed intermediate education with focus on computer science fundamentals. Achieved 70% marks while building foundation in programming concepts.",
      achievements: ["70% marks", "Computer Science focus", "Programming foundation"]
    }
  ]

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        delayChildren: 0.3,
        staggerChildren: 0.2
      }
    }
  }

  const itemVariants = {
    hidden: { x: -50, opacity: 0 },
    visible: {
      x: 0,
      opacity: 1,
      transition: {
        duration: 0.6
      }
    }
  }

  const timelineVariants = {
    hidden: { scaleY: 0 },
    visible: {
      scaleY: 1,
      transition: {
        duration: 1,
        delay: 0.5
      }
    }
  }

  return (
    <section id="education" className="bg-primary" style={{ padding: 'var(--space-5xl) 0' }}>
      <div className="container-responsive">
        <motion.div
          className="text-center"
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true }}
          variants={containerVariants}
          style={{ marginBottom: 'var(--space-4xl)' }}
        >
          <motion.h2
            className="text-responsive-5xl font-bold text-primary"
            variants={itemVariants}
            style={{ marginBottom: 'var(--space-md)' }}
          >
            Education & Experience
          </motion.h2>
          <motion.div
            className="mx-auto"
            variants={itemVariants}
            style={{
              width: '96px',
              height: '4px',
              background: 'var(--gradient-primary)',
              borderRadius: 'var(--radius-full)',
              marginBottom: 'var(--space-md)',
            }}
          />
          <motion.p
            className="text-secondary max-w-2xl mx-auto text-responsive-base"
            variants={itemVariants}
          >
            My academic journey and professional experiences that have shaped my skills and perspective.
          </motion.p>
        </motion.div>

        <div className="relative">
          {/* Timeline line - hidden on mobile, visible on desktop */}
          <motion.div
            className="absolute left-1/2 transform -translate-x-1/2 h-full origin-top hidden md:block"
            variants={timelineVariants}
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true }}
            style={{
              width: '4px',
              background: 'var(--gradient-primary)',
              borderRadius: 'var(--radius-full)',
            }}
          />

          <motion.div
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true }}
            variants={containerVariants}
            style={{ display: 'flex', flexDirection: 'column', gap: 'var(--space-3xl)' }}
          >
            {timeline.map((item, index) => (
              <motion.div
                key={item.id}
                className={`flex items-center ${
                  index % 2 === 0
                    ? 'flex-col md:flex-row'
                    : 'flex-col md:flex-row-reverse'
                }`}
                variants={itemVariants}
              >
                <div className={`w-full md:w-1/2 ${
                  index % 2 === 0 ? 'md:pr-8' : 'md:pl-8'
                }`}>
                  <motion.div
                    className="card-base touch-target"
                    whileHover={{ scale: 1.02 }}
                    transition={{ duration: 0.2 }}
                    style={{ padding: 'var(--space-lg)' }}
                  >
                    <div
                      className="flex items-center"
                      style={{ marginBottom: 'var(--space-md)' }}
                    >
                      <div
                        className="rounded-full"
                        style={{
                          width: '12px',
                          height: '12px',
                          backgroundColor: item.type === 'education' ? 'var(--color-primary)' : 'var(--color-secondary)',
                          marginRight: 'var(--space-md)',
                        }}
                      />
                      <span className="text-quaternary text-responsive-sm font-medium">
                        {item.period}
                      </span>
                    </div>

                    <h3
                      className="text-responsive-xl font-semibold text-primary"
                      style={{ marginBottom: 'var(--space-sm)' }}
                    >
                      {item.title}
                    </h3>

                    <h4
                      className="text-brand-primary font-medium text-responsive-base"
                      style={{ marginBottom: 'var(--space-md)' }}
                    >
                      {item.institution}
                    </h4>

                    <p
                      className="text-secondary leading-relaxed text-responsive-sm"
                      style={{ marginBottom: 'var(--space-md)' }}
                    >
                      {item.description}
                    </p>

                    <div style={{ display: 'flex', flexDirection: 'column', gap: 'var(--space-sm)' }}>
                      <h5 className="text-responsive-xs font-semibold text-quaternary uppercase tracking-wide">
                        Key Achievements
                      </h5>
                      <ul style={{ display: 'flex', flexDirection: 'column', gap: 'var(--space-xs)' }}>
                        {item.achievements.map((achievement, achIndex) => (
                          <li key={achIndex} className="text-secondary text-responsive-xs flex items-center">
                            <span
                              className="rounded-full"
                              style={{
                                width: '6px',
                                height: '6px',
                                backgroundColor: 'var(--color-primary)',
                                marginRight: 'var(--space-sm)',
                              }}
                            />
                            {achievement}
                          </li>
                        ))}
                      </ul>
                    </div>
                  </motion.div>
                </div>

                {/* Timeline dot - only visible on desktop */}
                <motion.div
                  className="relative z-10 hidden md:block"
                  whileHover={{ scale: 1.2 }}
                >
                  <div
                    className="rounded-full"
                    style={{
                      width: '24px',
                      height: '24px',
                      backgroundColor: item.type === 'education' ? 'var(--color-primary)' : 'var(--color-secondary)',
                      border: '4px solid var(--bg-primary)',
                    }}
                  />
                </motion.div>

                <div className="hidden md:block md:w-1/2" />
              </motion.div>
            ))}
          </motion.div>
        </div>
      </div>
    </section>
  )
}

export default Education
