import React from 'react'
import { motion } from 'framer-motion'

const Blog = () => {
  const blogPosts = [
    {
      id: 1,
      title: "Building Scalable MERN Applications: Best Practices",
      description: "Explore the essential patterns and practices for creating maintainable and scalable MERN stack applications. From project structure to deployment strategies.",
      date: "Dec 15, 2024",
      readTime: "8 min read",
      category: "Web Development",
      link: "https://medium.com/@syedshaheerabbas",
      image: "/api/placeholder/400/200",
      tags: ["React", "Node.js", "MongoDB", "Express"]
    },
    {
      id: 2,
      title: "Integrating .NET with Modern Frontend Frameworks",
      description: "A comprehensive guide on connecting .NET backend services with React applications. Learn about API design, authentication, and real-time communication.",
      date: "Nov 28, 2024",
      readTime: "12 min read",
      category: "Full-Stack",
      link: "https://dev.to/syedshaheerabbas",
      image: "/api/placeholder/400/200",
      tags: [".NET", "React", "API", "Authentication"]
    },
    {
      id: 3,
      title: "From Student to Developer: My Journey at NUML",
      description: "Sharing my experiences as a Computer Science student, organizing tech events, and building projects that matter. Lessons learned and advice for fellow students.",
      date: "Oct 20, 2024",
      readTime: "6 min read",
      category: "Career",
      link: "https://hashnode.com/@syedshaheerabbas",
      image: "/api/placeholder/400/200",
      tags: ["Career", "Student Life", "Tech Events", "Learning"]
    }
  ]

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        delayChildren: 0.3,
        staggerChildren: 0.2
      }
    }
  }

  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        duration: 0.5
      }
    }
  }

  const cardVariants = {
    hidden: { y: 50, opacity: 0, scale: 0.9 },
    visible: {
      y: 0,
      opacity: 1,
      scale: 1,
      transition: {
        duration: 0.6,
        type: "spring",
        stiffness: 100
      }
    }
  }

  return (
    <section id="blog" className="py-20 bg-gray-900">
      <div className="max-w-6xl mx-auto px-4">
        <motion.div
          className="text-center mb-16"
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true }}
          variants={containerVariants}
        >
          <motion.h2
            className="text-4xl md:text-5xl font-bold text-white mb-4"
            variants={itemVariants}
          >
            My Writings
          </motion.h2>
          <motion.div
            className="w-24 h-1 bg-gradient-to-r from-purple-500 to-pink-500 mx-auto mb-4"
            variants={itemVariants}
          />
          <motion.p
            className="text-gray-400 max-w-2xl mx-auto"
            variants={itemVariants}
          >
            Sharing insights, tutorials, and experiences from my journey as a full-stack developer.
          </motion.p>
        </motion.div>

        <motion.div
          className="grid md:grid-cols-2 lg:grid-cols-3 gap-8"
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true }}
          variants={containerVariants}
        >
          {blogPosts.map((post) => (
            <motion.article
              key={post.id}
              className="bg-gray-800 rounded-lg overflow-hidden shadow-lg group"
              variants={cardVariants}
              whileHover={{ y: -5 }}
            >
              {/* Image placeholder */}
              <div className="relative h-48 bg-gradient-to-br from-purple-600 to-pink-600 overflow-hidden">
                <div className="absolute inset-0 flex items-center justify-center">
                  <span className="text-white text-lg font-semibold">Blog Image</span>
                </div>
                <motion.div
                  className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 transition-all duration-300"
                  whileHover={{ scale: 1.05 }}
                />
                
                {/* Category badge */}
                <div className="absolute top-4 left-4">
                  <span className="px-3 py-1 bg-purple-600 text-white text-xs font-semibold rounded-full">
                    {post.category}
                  </span>
                </div>
              </div>

              <div className="p-6">
                {/* Meta info */}
                <div className="flex items-center text-gray-400 text-sm mb-3">
                  <span>{post.date}</span>
                  <span className="mx-2">•</span>
                  <span>{post.readTime}</span>
                </div>

                {/* Title */}
                <h3 className="text-xl font-semibold text-white mb-3 group-hover:text-purple-400 transition-colors duration-300">
                  {post.title}
                </h3>

                {/* Description */}
                <p className="text-gray-300 mb-4 leading-relaxed line-clamp-3">
                  {post.description}
                </p>

                {/* Tags */}
                <div className="flex flex-wrap gap-2 mb-4">
                  {post.tags.map((tag, index) => (
                    <span
                      key={index}
                      className="px-2 py-1 bg-gray-700 text-purple-400 text-xs rounded-full"
                    >
                      {tag}
                    </span>
                  ))}
                </div>

                {/* Read more link */}
                <motion.a
                  href={post.link}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="inline-flex items-center text-purple-400 hover:text-purple-300 font-medium transition-colors duration-300"
                  whileHover={{ x: 5 }}
                >
                  Read More
                  <motion.span
                    className="ml-2"
                    animate={{ x: [0, 5, 0] }}
                    transition={{ duration: 1.5, repeat: Infinity }}
                  >
                    →
                  </motion.span>
                </motion.a>
              </div>
            </motion.article>
          ))}
        </motion.div>

        {/* View all posts link */}
        <motion.div
          className="text-center mt-12"
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true }}
          variants={itemVariants}
        >
          <motion.a
            href="https://medium.com/@syedshaheerabbas"
            target="_blank"
            rel="noopener noreferrer"
            className="inline-flex items-center px-6 py-3 bg-gradient-to-r from-purple-500 to-pink-500 text-white font-semibold rounded-lg hover:shadow-lg transition-all duration-300"
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
            View All Posts
            <motion.span
              className="ml-2"
              animate={{ x: [0, 3, 0] }}
              transition={{ duration: 1, repeat: Infinity }}
            >
              📚
            </motion.span>
          </motion.a>
        </motion.div>
      </div>
    </section>
  )
}

export default Blog
