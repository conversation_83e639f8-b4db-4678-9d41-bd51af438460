import React from 'react'
import { motion } from 'framer-motion'

const Blog = () => {
  const blogPosts = [
    {
      id: 1,
      title: "Building Scalable MERN Applications: Best Practices",
      description: "Explore the essential patterns and practices for creating maintainable and scalable MERN stack applications. From project structure to deployment strategies.",
      date: "Dec 15, 2024",
      readTime: "8 min read",
      category: "Web Development",
      link: "https://medium.com/@syedshaheerabbas",
      image: "/api/placeholder/400/200",
      tags: ["React", "Node.js", "MongoDB", "Express"]
    },
    {
      id: 2,
      title: "Integrating .NET with Modern Frontend Frameworks",
      description: "A comprehensive guide on connecting .NET backend services with React applications. Learn about API design, authentication, and real-time communication.",
      date: "Nov 28, 2024",
      readTime: "12 min read",
      category: "Full-Stack",
      link: "https://dev.to/syedshaheerabbas",
      image: "/api/placeholder/400/200",
      tags: [".NET", "React", "API", "Authentication"]
    },
    {
      id: 3,
      title: "From Student to Developer: My Journey at NUML",
      description: "Sharing my experiences as a Computer Science student, organizing tech events, and building projects that matter. Lessons learned and advice for fellow students.",
      date: "Oct 20, 2024",
      readTime: "6 min read",
      category: "Career",
      link: "https://hashnode.com/@syedshaheerabbas",
      image: "/api/placeholder/400/200",
      tags: ["Career", "Student Life", "Tech Events", "Learning"]
    }
  ]

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        delayChildren: 0.3,
        staggerChildren: 0.2
      }
    }
  }

  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        duration: 0.5
      }
    }
  }

  const cardVariants = {
    hidden: { y: 50, opacity: 0, scale: 0.9 },
    visible: {
      y: 0,
      opacity: 1,
      scale: 1,
      transition: {
        duration: 0.6,
        type: "spring",
        stiffness: 100
      }
    }
  }

  return (
    <section id="blog" className="bg-primary" style={{ padding: 'var(--space-5xl) 0' }}>
      <div className="container-responsive">
        <motion.div
          className="text-center"
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true }}
          variants={containerVariants}
          style={{ marginBottom: 'var(--space-4xl)' }}
        >
          <motion.h2
            className="text-responsive-5xl font-bold text-primary"
            variants={itemVariants}
            style={{ marginBottom: 'var(--space-md)' }}
          >
            My Writings
          </motion.h2>
          <motion.div
            className="mx-auto"
            variants={itemVariants}
            style={{
              width: '96px',
              height: '4px',
              background: 'var(--gradient-primary)',
              borderRadius: 'var(--radius-full)',
              marginBottom: 'var(--space-md)',
            }}
          />
          <motion.p
            className="text-secondary max-w-2xl mx-auto text-responsive-base"
            variants={itemVariants}
          >
            Sharing insights, tutorials, and experiences from my journey as a full-stack developer.
          </motion.p>
        </motion.div>

        <motion.div
          className="grid-responsive-3"
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true }}
          variants={containerVariants}
        >
          {blogPosts.map((post) => (
            <motion.article
              key={post.id}
              className="card-base overflow-hidden group touch-target"
              variants={cardVariants}
              whileHover={{ y: -5 }}
            >
              {/* Image placeholder */}
              <div
                className="relative overflow-hidden"
                style={{
                  height: 'clamp(180px, 25vw, 200px)',
                  background: 'var(--gradient-primary)',
                }}
              >
                <div className="absolute inset-0 flex items-center justify-center">
                  <span className="text-inverse text-responsive-lg font-semibold">Blog Image</span>
                </div>
                <motion.div
                  className="absolute inset-0 transition-normal"
                  style={{
                    backgroundColor: 'rgba(0, 0, 0, 0)',
                    groupHover: { backgroundColor: 'rgba(0, 0, 0, 0.2)' }
                  }}
                  whileHover={{ scale: 1.05 }}
                />

                {/* Category badge */}
                <div
                  className="absolute"
                  style={{
                    top: 'var(--space-md)',
                    left: 'var(--space-md)',
                  }}
                >
                  <span
                    className="text-inverse text-responsive-xs font-semibold"
                    style={{
                      padding: 'var(--space-xs) var(--space-md)',
                      backgroundColor: 'var(--color-primary)',
                      borderRadius: 'var(--radius-full)',
                    }}
                  >
                    {post.category}
                  </span>
                </div>
              </div>

              <div style={{ padding: 'var(--space-lg)' }}>
                {/* Meta info */}
                <div
                  className="flex items-center text-quaternary text-responsive-sm"
                  style={{
                    marginBottom: 'var(--space-md)',
                    gap: 'var(--space-sm)',
                  }}
                >
                  <span>{post.date}</span>
                  <span>•</span>
                  <span>{post.readTime}</span>
                </div>

                {/* Title */}
                <h3
                  className="text-responsive-xl font-semibold text-primary group-hover:text-brand-primary transition-normal"
                  style={{ marginBottom: 'var(--space-md)' }}
                >
                  {post.title}
                </h3>

                {/* Description */}
                <p
                  className="text-secondary leading-relaxed line-clamp-3 text-responsive-sm"
                  style={{ marginBottom: 'var(--space-md)' }}
                >
                  {post.description}
                </p>

                {/* Tags */}
                <div
                  className="flex flex-wrap"
                  style={{
                    gap: 'var(--space-sm)',
                    marginBottom: 'var(--space-md)',
                  }}
                >
                  {post.tags.map((tag, index) => (
                    <span
                      key={index}
                      className="text-brand-primary text-responsive-xs"
                      style={{
                        padding: 'var(--space-xs) var(--space-sm)',
                        backgroundColor: 'var(--bg-tertiary)',
                        borderRadius: 'var(--radius-full)',
                      }}
                    >
                      {tag}
                    </span>
                  ))}
                </div>

                {/* Read more link */}
                <motion.a
                  href={post.link}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="inline-flex items-center text-brand-primary font-medium transition-normal text-responsive-sm"
                  whileHover={{ x: 5 }}
                  style={{
                    textDecoration: 'none',
                    gap: 'var(--space-sm)',
                  }}
                >
                  Read More
                  <motion.span
                    animate={{ x: [0, 5, 0] }}
                    transition={{ duration: 1.5, repeat: Infinity }}
                  >
                    →
                  </motion.span>
                </motion.a>
              </div>
            </motion.article>
          ))}
        </motion.div>

        {/* View all posts link */}
        <motion.div
          className="text-center"
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true }}
          variants={itemVariants}
          style={{ marginTop: 'var(--space-3xl)' }}
        >
          <motion.a
            href="https://medium.com/@syedshaheerabbas"
            target="_blank"
            rel="noopener noreferrer"
            className="btn-primary inline-flex items-center touch-target"
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            style={{
              textDecoration: 'none',
              gap: 'var(--space-sm)',
            }}
          >
            View All Posts
            <motion.span
              animate={{ x: [0, 3, 0] }}
              transition={{ duration: 1, repeat: Infinity }}
            >
              📚
            </motion.span>
          </motion.a>
        </motion.div>
      </div>
    </section>
  )
}

export default Blog
