import React from 'react'
import { motion } from 'framer-motion'

const GitHubRepos = () => {
  const repositories = [
    {
      id: 1,
      name: "landtrust-blockchain",
      description: "A secure digital land registry system using AI-powered OCR and blockchain technology for immutable property records.",
      language: "JavaScript",
      languageColor: "#f1e05a",
      stars: 24,
      forks: 8,
      topics: ["blockchain", "react", "fastapi", "ocr", "property-registry"],
      url: "https://github.com/syedshaheerabbas/landtrust-blockchain",
      updated: "2 days ago"
    },
    {
      id: 2,
      name: "mern-ecommerce-platform",
      description: "Full-stack e-commerce solution with real-time inventory management, secure payments, and comprehensive admin dashboard.",
      language: "TypeScript",
      languageColor: "#2b7489",
      stars: 18,
      forks: 5,
      topics: ["mern", "ecommerce", "react", "nodejs", "mongodb"],
      url: "https://github.com/syedshaheerabbas/mern-ecommerce-platform",
      updated: "1 week ago"
    },
    {
      id: 3,
      name: "dotnet-web-api",
      description: "Enterprise-level .NET Web API with clean architecture, JWT authentication, and comprehensive documentation.",
      language: "C#",
      languageColor: "#239120",
      stars: 15,
      forks: 3,
      topics: ["dotnet", "webapi", "csharp", "jwt", "clean-architecture"],
      url: "https://github.com/syedshaheerabbas/dotnet-web-api",
      updated: "3 days ago"
    },
    {
      id: 4,
      name: "react-portfolio",
      description: "Modern, responsive portfolio website built with React, TypeScript, Tailwind CSS, and Framer Motion animations.",
      language: "TypeScript",
      languageColor: "#2b7489",
      stars: 12,
      forks: 4,
      topics: ["react", "typescript", "tailwindcss", "framer-motion", "portfolio"],
      url: "https://github.com/syedshaheerabbas/react-portfolio",
      updated: "1 day ago"
    }
  ]

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        delayChildren: 0.3,
        staggerChildren: 0.15
      }
    }
  }

  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        duration: 0.5
      }
    }
  }

  const cardVariants = {
    hidden: { y: 50, opacity: 0, scale: 0.9 },
    visible: {
      y: 0,
      opacity: 1,
      scale: 1,
      transition: {
        duration: 0.6,
        type: "spring",
        stiffness: 100
      }
    }
  }

  return (
    <section id="github-repos" className="bg-secondary" style={{ padding: 'var(--space-5xl) 0' }}>
      <div className="container-responsive">
        <motion.div
          className="text-center"
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true }}
          variants={containerVariants}
          style={{ marginBottom: 'var(--space-4xl)' }}
        >
          <motion.h2
            className="text-responsive-5xl font-bold text-primary"
            variants={itemVariants}
            style={{ marginBottom: 'var(--space-md)' }}
          >
            Open Source Work
          </motion.h2>
          <motion.div
            className="mx-auto"
            variants={itemVariants}
            style={{
              width: '96px',
              height: '4px',
              background: 'var(--gradient-primary)',
              borderRadius: 'var(--radius-full)',
              marginBottom: 'var(--space-md)',
            }}
          />
          <motion.p
            className="text-secondary max-w-2xl mx-auto text-responsive-base"
            variants={itemVariants}
          >
            Explore my featured repositories showcasing clean code, innovative solutions, and collaborative development.
          </motion.p>
        </motion.div>

        <motion.div
          className="grid-responsive-2"
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true }}
          variants={containerVariants}
        >
          {repositories.map((repo) => (
            <motion.div
              key={repo.id}
              className="card-base touch-target"
              variants={cardVariants}
              whileHover={{
                y: -5,
                scale: 1.02,
                boxShadow: "0 10px 30px rgba(139, 92, 246, 0.3)"
              }}
              style={{
                padding: 'var(--space-lg)',
                borderColor: 'var(--border-primary)',
              }}
            >
              {/* Header */}
              <div
                className="flex items-start justify-between"
                style={{ marginBottom: 'var(--space-md)' }}
              >
                <div className="flex items-center" style={{ gap: 'var(--space-md)' }}>
                  <motion.div
                    className="rounded-full"
                    animate={{ scale: [1, 1.2, 1] }}
                    transition={{ duration: 2, repeat: Infinity }}
                    style={{
                      width: '16px',
                      height: '16px',
                      backgroundColor: 'var(--color-primary)',
                    }}
                  />
                  <motion.h3
                    className="text-responsive-lg font-semibold text-primary hover:text-brand-primary transition-normal"
                    whileHover={{ scale: 1.05 }}
                  >
                    <a
                      href={repo.url}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="hover:underline"
                      style={{ textDecoration: 'none' }}
                    >
                      {repo.name}
                    </a>
                  </motion.h3>
                </div>
                <motion.a
                  href={repo.url}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-quaternary hover:text-primary transition-normal"
                  whileHover={{ scale: 1.1, rotate: 15 }}
                  whileTap={{ scale: 0.9 }}
                >
                  <svg
                    className="w-5 h-5"
                    fill="currentColor"
                    viewBox="0 0 20 20"
                    style={{ width: 'var(--text-lg)', height: 'var(--text-lg)' }}
                  >
                    <path fillRule="evenodd" d="M10.293 3.293a1 1 0 011.414 0l6 6a1 1 0 010 1.414l-6 6a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-4.293-4.293a1 1 0 010-1.414z" clipRule="evenodd" />
                  </svg>
                </motion.a>
              </div>

              {/* Description */}
              <p
                className="text-secondary leading-relaxed text-responsive-sm"
                style={{ marginBottom: 'var(--space-md)' }}
              >
                {repo.description}
              </p>

              {/* Topics */}
              <div className="flex flex-wrap gap-2 mb-4">
                {repo.topics.slice(0, 3).map((topic, index) => (
                  <motion.span
                    key={index}
                    className="px-2 py-1 bg-purple-600/20 text-purple-400 text-xs rounded-full border border-purple-600/30"
                    initial={{ opacity: 0, scale: 0 }}
                    animate={{ opacity: 1, scale: 1 }}
                    transition={{ delay: index * 0.1 + 0.5 }}
                    whileHover={{ scale: 1.1 }}
                  >
                    {topic}
                  </motion.span>
                ))}
                {repo.topics.length > 3 && (
                  <span className="px-2 py-1 bg-gray-700 text-gray-400 text-xs rounded-full">
                    +{repo.topics.length - 3}
                  </span>
                )}
              </div>

              {/* Stats and Language */}
              <div className="flex items-center justify-between text-sm text-gray-400">
                <div className="flex items-center space-x-4">
                  <div className="flex items-center">
                    <div 
                      className="w-3 h-3 rounded-full mr-2"
                      style={{ backgroundColor: repo.languageColor }}
                    />
                    <span>{repo.language}</span>
                  </div>
                  
                  <motion.div 
                    className="flex items-center"
                    whileHover={{ scale: 1.1 }}
                  >
                    <svg className="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                      <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                    </svg>
                    <span>{repo.stars}</span>
                  </motion.div>

                  <motion.div 
                    className="flex items-center"
                    whileHover={{ scale: 1.1 }}
                  >
                    <svg className="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M7.707 3.293a1 1 0 010 1.414L5.414 7H11a7 7 0 017 7v2a1 1 0 11-2 0v-2a5 5 0 00-5-5H5.414l2.293 2.293a1 1 0 11-1.414 1.414L2.586 7l3.707-3.707a1 1 0 011.414 0z" clipRule="evenodd" />
                    </svg>
                    <span>{repo.forks}</span>
                  </motion.div>
                </div>

                <span className="text-xs">Updated {repo.updated}</span>
              </div>
            </motion.div>
          ))}
        </motion.div>

        {/* View all repositories link */}
        <motion.div
          className="text-center mt-12"
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true }}
          variants={itemVariants}
        >
          <motion.a
            href="https://github.com/syedshaheerabbas"
            target="_blank"
            rel="noopener noreferrer"
            className="inline-flex items-center px-6 py-3 bg-gradient-to-r from-purple-500 to-pink-500 text-white font-semibold rounded-lg hover:shadow-lg transition-all duration-300"
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
            View All Repositories
            <motion.span
              className="ml-2"
              animate={{ x: [0, 3, 0] }}
              transition={{ duration: 1, repeat: Infinity }}
            >
              🐙
            </motion.span>
          </motion.a>
        </motion.div>
      </div>
    </section>
  )
}

export default GitHubRepos
