import React from 'react'
import { motion } from 'framer-motion'
import { useTheme } from '../contexts/ThemeContext'

const ThemeToggle = () => {
  const { theme, toggleTheme } = useTheme()

  const toggleVariants = {
    light: { rotate: 0 },
    dark: { rotate: 180 }
  }

  const iconVariants = {
    hidden: { scale: 0, opacity: 0 },
    visible: { 
      scale: 1, 
      opacity: 1,
      transition: {
        type: "spring",
        stiffness: 500,
        damping: 30
      }
    }
  }

  return (
    <motion.button
      onClick={toggleTheme}
      className="fixed top-6 right-6 z-50 p-3 bg-gray-800/90 dark:bg-gray-200/90 backdrop-blur-lg border border-gray-700/50 dark:border-gray-300/50 rounded-full shadow-lg hover:shadow-xl transition-all duration-300"
      whileHover={{ scale: 1.1 }}
      whileTap={{ scale: 0.9 }}
      variants={toggleVariants}
      animate={theme}
      initial={false}
    >
      <motion.div
        className="relative w-6 h-6"
        variants={iconVariants}
        key={theme}
        initial="hidden"
        animate="visible"
      >
        {theme === 'dark' ? (
          <motion.div
            className="text-yellow-400 text-xl"
            animate={{ 
              rotate: [0, 360],
              scale: [1, 1.2, 1]
            }}
            transition={{ 
              rotate: { duration: 2, repeat: Infinity, ease: "linear" },
              scale: { duration: 1, repeat: Infinity }
            }}
          >
            ☀️
          </motion.div>
        ) : (
          <motion.div
            className="text-blue-400 text-xl"
            animate={{ 
              rotate: [0, -10, 10, 0],
              scale: [1, 1.1, 1]
            }}
            transition={{ 
              duration: 2, 
              repeat: Infinity 
            }}
          >
            🌙
          </motion.div>
        )}
      </motion.div>

      {/* Tooltip */}
      <motion.div
        className="absolute -bottom-12 left-1/2 transform -translate-x-1/2 bg-gray-900 dark:bg-gray-100 text-white dark:text-gray-900 text-xs px-3 py-1 rounded-lg opacity-0 pointer-events-none whitespace-nowrap"
        whileHover={{ opacity: 1, y: -2 }}
        transition={{ duration: 0.2 }}
      >
        Switch to {theme === 'dark' ? 'light' : 'dark'} mode
        <div className="absolute top-full left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-2 border-r-2 border-t-2 border-transparent border-t-gray-900 dark:border-t-gray-100" />
      </motion.div>

      {/* Ripple effect */}
      <motion.div
        className="absolute inset-0 rounded-full border-2 border-purple-500 opacity-0"
        animate={{
          scale: [1, 1.5, 2],
          opacity: [0.5, 0.2, 0]
        }}
        transition={{
          duration: 1.5,
          repeat: Infinity,
          repeatDelay: 2
        }}
      />
    </motion.button>
  )
}

export default ThemeToggle
