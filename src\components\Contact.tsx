import { useState } from 'react'
import { motion } from 'framer-motion'

const Contact = () => {
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    message: ''
  })

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value
    })
  }

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    // Handle form submission here
    console.log('Form submitted:', formData)
    // Reset form
    setFormData({ name: '', email: '', message: '' })
  }

  const socialLinks = [
    {
      name: "Email",
      url: "mailto:<EMAIL>",
      icon: "📧"
    },
    {
      name: "Phone",
      url: "tel:+923484587063",
      icon: "📱"
    },
    {
      name: "LinkedIn",
      url: "https://www.linkedin.com/in/sy-shaheer-abbas",
      icon: "💼"
    },
    {
      name: "GitH<PERSON>",
      url: "https://github.com/syedshaheerabbas",
      icon: "🐙"
    }
  ]

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        delayChildren: 0.3,
        staggerChildren: 0.2
      }
    }
  }

  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        duration: 0.5
      }
    }
  }

  return (
    <section id="contact" className="bg-primary" style={{ padding: 'var(--space-5xl) 0' }}>
      <div className="container-responsive">
        <motion.div
          className="text-center"
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true }}
          variants={containerVariants}
          style={{ marginBottom: 'var(--space-4xl)' }}
        >
          <motion.h2
            className="text-responsive-5xl font-bold text-primary"
            variants={itemVariants}
            style={{ marginBottom: 'var(--space-md)' }}
          >
            Get In Touch
          </motion.h2>
          <motion.div
            className="mx-auto"
            variants={itemVariants}
            style={{
              width: '96px',
              height: '4px',
              background: 'var(--gradient-primary)',
              borderRadius: 'var(--radius-full)',
              marginBottom: 'var(--space-md)',
            }}
          />
          <motion.p
            className="text-secondary max-w-2xl mx-auto text-responsive-base"
            variants={itemVariants}
          >
            I'm always open to discussing new opportunities, MERN & .NET projects,
            or collaborating on innovative web development solutions.
          </motion.p>
        </motion.div>

        <div className="grid-responsive-2">
          {/* Contact Form */}
          <motion.div
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true }}
            variants={containerVariants}
          >
            <motion.h3
              className="text-responsive-2xl font-semibold text-primary"
              variants={itemVariants}
              style={{ marginBottom: 'var(--space-lg)' }}
            >
              Send me a message
            </motion.h3>
            
            <motion.form
              onSubmit={handleSubmit}
              variants={itemVariants}
              style={{ display: 'flex', flexDirection: 'column', gap: 'var(--space-lg)' }}
            >
              <div>
                <label
                  htmlFor="name"
                  className="block text-responsive-sm font-medium text-secondary"
                  style={{ marginBottom: 'var(--space-sm)' }}
                >
                  Name
                </label>
                <input
                  type="text"
                  id="name"
                  name="name"
                  value={formData.name}
                  onChange={handleChange}
                  required
                  className="w-full bg-input text-primary placeholder-quaternary focus:border-focus transition-normal touch-target"
                  placeholder="Your Name"
                  style={{
                    padding: 'var(--space-md)',
                    border: '1px solid var(--border-primary)',
                    borderRadius: 'var(--radius-lg)',
                    fontSize: 'var(--text-base)',
                  }}
                />
              </div>
              
              <div>
                <label
                  htmlFor="email"
                  className="block text-responsive-sm font-medium text-secondary"
                  style={{ marginBottom: 'var(--space-sm)' }}
                >
                  Email
                </label>
                <input
                  type="email"
                  id="email"
                  name="email"
                  value={formData.email}
                  onChange={handleChange}
                  required
                  className="w-full bg-input text-primary placeholder-quaternary focus:border-focus transition-normal touch-target"
                  placeholder="<EMAIL>"
                  style={{
                    padding: 'var(--space-md)',
                    border: '1px solid var(--border-primary)',
                    borderRadius: 'var(--radius-lg)',
                    fontSize: 'var(--text-base)',
                  }}
                />
              </div>
              
              <div>
                <label
                  htmlFor="message"
                  className="block text-responsive-sm font-medium text-secondary"
                  style={{ marginBottom: 'var(--space-sm)' }}
                >
                  Message
                </label>
                <textarea
                  id="message"
                  name="message"
                  value={formData.message}
                  onChange={handleChange}
                  required
                  rows={5}
                  className="w-full bg-input text-primary placeholder-quaternary focus:border-focus transition-normal resize-none"
                  placeholder="Tell me about your project or just say hello!"
                  style={{
                    padding: 'var(--space-md)',
                    border: '1px solid var(--border-primary)',
                    borderRadius: 'var(--radius-lg)',
                    fontSize: 'var(--text-base)',
                    minHeight: '120px',
                  }}
                />
              </div>

              <motion.button
                type="submit"
                className="btn-primary w-full touch-target"
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
              >
                Send Message
              </motion.button>
            </motion.form>
          </motion.div>

          {/* Contact Info */}
          <motion.div
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true }}
            variants={containerVariants}
            style={{ display: 'flex', flexDirection: 'column', gap: 'var(--space-2xl)' }}
          >
            <motion.div variants={itemVariants}>
              <h3
                className="text-responsive-2xl font-semibold text-primary"
                style={{ marginBottom: 'var(--space-lg)' }}
              >
                Let's connect
              </h3>
              <p
                className="text-secondary leading-relaxed text-responsive-base"
                style={{ marginBottom: 'var(--space-lg)' }}
              >
                Whether you have a MERN stack project, .NET development needs, or want to
                collaborate on innovative web solutions, I'd love to hear from you. Let's
                build something amazing together!
              </p>

              <div style={{ display: 'flex', flexDirection: 'column', gap: 'var(--space-md)' }}>
                <div
                  className="flex items-center text-responsive-base"
                  style={{ gap: 'var(--space-md)' }}
                >
                  <span className="text-brand-primary">📧</span>
                  <span className="text-secondary"><EMAIL></span>
                </div>
                <div
                  className="flex items-center text-responsive-base"
                  style={{ gap: 'var(--space-md)' }}
                >
                  <span className="text-brand-primary">📱</span>
                  <span className="text-secondary">+92 348 4587063</span>
                </div>
                <div
                  className="flex items-center text-responsive-base"
                  style={{ gap: 'var(--space-md)' }}
                >
                  <span className="text-brand-primary">📍</span>
                  <span className="text-secondary">Lahore, Pakistan</span>
                </div>
              </div>
            </motion.div>

            <motion.div
              className="grid grid-cols-1 sm:grid-cols-2"
              variants={containerVariants}
              style={{ gap: 'var(--space-md)' }}
            >
              {socialLinks.map((link, index) => (
                <motion.a
                  key={index}
                  href={link.url}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="card-base hover:bg-hover transition-normal touch-target"
                  variants={itemVariants}
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  style={{
                    display: 'flex',
                    alignItems: 'center',
                    padding: 'var(--space-md)',
                    textDecoration: 'none',
                    gap: 'var(--space-md)',
                  }}
                >
                  <span className="text-responsive-xl">{link.icon}</span>
                  <span className="text-secondary font-medium text-responsive-base">{link.name}</span>
                </motion.a>
              ))}
            </motion.div>

            <motion.div
              className="bg-gray-700 p-6 rounded-lg"
              variants={itemVariants}
            >
              <h4 className="text-lg font-semibold text-white mb-3">
                Quick Response
              </h4>
              <p className="text-gray-300 text-sm">
                I typically respond to emails within 24 hours. For urgent matters, 
                feel free to reach out on LinkedIn for a faster response.
              </p>
            </motion.div>
          </motion.div>
        </div>
      </div>
    </section>
  )
}

export default Contact
