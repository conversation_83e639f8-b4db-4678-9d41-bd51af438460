import { motion } from 'framer-motion'

const Projects = () => {
  const projects = [
    {
      id: 1,
      title: "LandTrust – Blockchain Land Registry",
      description: "A secure digital land registry system using AI-powered OCR and blockchain for immutability. Streamlines property verification and reduces fraud.",
      tech: ["React", "FastAPI", "OCR", "Blockchain", "Python"],
      github: "https://github.com/syedshaheerabbas",
      demo: "#",
      image: "/api/placeholder/400/250"
    },
    {
      id: 2,
      title: "MERN Stack E-Commerce Platform",
      description: "Full-stack e-commerce solution with real-time inventory management, secure payment processing, and admin dashboard.",
      tech: ["React", "Node.js", "Express.js", "MongoDB", "JWT"],
      github: "https://github.com/syedshaheerabbas",
      demo: "#",
      image: "/api/placeholder/400/250"
    },
    {
      id: 3,
      title: ".NET Web Application",
      description: "Enterprise-level web application built with .NET framework featuring user authentication, data management, and responsive design.",
      tech: [".NET", "C#", "SQL Server", "Bootstrap", "Entity Framework"],
      github: "https://github.com/syedshaheerabbas",
      demo: "#",
      image: "/api/placeholder/400/250"
    },
    {
      id: 4,
      title: "React Portfolio Website",
      description: "Modern, responsive portfolio website with smooth animations, dark theme, and optimized performance using React and Framer Motion.",
      tech: ["React", "TypeScript", "Tailwind CSS", "Framer Motion", "Vite"],
      github: "https://github.com/syedshaheerabbas",
      demo: "#",
      image: "/api/placeholder/400/250"
    }
  ]

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        delayChildren: 0.3,
        staggerChildren: 0.2
      }
    }
  }

  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        duration: 0.5
      }
    }
  }

  const cardVariants = {
    hidden: { y: 50, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        duration: 0.6
      }
    }
  }

  return (
    <section id="projects" className="bg-primary" style={{ padding: 'var(--space-5xl) 0' }}>
      <div className="container-responsive">
        <motion.div
          className="text-center"
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true }}
          variants={containerVariants}
          style={{ marginBottom: 'var(--space-4xl)' }}
        >
          <motion.h2
            className="text-responsive-5xl font-bold text-primary"
            variants={itemVariants}
            style={{ marginBottom: 'var(--space-md)' }}
          >
            Featured Projects
          </motion.h2>
          <motion.div
            className="mx-auto"
            variants={itemVariants}
            style={{
              width: '96px',
              height: '4px',
              background: 'var(--gradient-primary)',
              borderRadius: 'var(--radius-full)',
              marginBottom: 'var(--space-md)',
            }}
          />
          <motion.p
            className="text-secondary max-w-2xl mx-auto text-responsive-base"
            variants={itemVariants}
          >
            Here are some of my recent projects that showcase my skills in MERN stack,
            .NET development, and innovative problem-solving.
          </motion.p>
        </motion.div>

        <motion.div
          className="grid-responsive-2"
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true }}
          variants={containerVariants}
        >
          {projects.map((project) => (
            <motion.div
              key={project.id}
              className="card-base overflow-hidden touch-target"
              variants={cardVariants}
              whileHover={{ y: -5 }}
            >
              <div className="relative group">
                <div
                  className="w-full flex items-center justify-center"
                  style={{
                    height: 'clamp(180px, 25vw, 200px)',
                    background: 'var(--gradient-primary)',
                  }}
                >
                  <span className="text-inverse text-responsive-lg font-semibold">Project Image</span>
                </div>
                <div
                  className="absolute inset-0 opacity-0 group-hover:opacity-100 flex items-center justify-center transition-normal"
                  style={{
                    backgroundColor: 'var(--bg-overlay)',
                    gap: 'var(--space-md)',
                  }}
                >
                  <motion.a
                    href={project.github}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="btn-secondary"
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                    style={{
                      padding: 'var(--space-sm) var(--space-md)',
                      fontSize: 'var(--text-sm)',
                      textDecoration: 'none',
                    }}
                  >
                    GitHub
                  </motion.a>
                  <motion.a
                    href={project.demo}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="btn-primary"
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                    style={{
                      padding: 'var(--space-sm) var(--space-md)',
                      fontSize: 'var(--text-sm)',
                      textDecoration: 'none',
                    }}
                  >
                    Live Demo
                  </motion.a>
                </div>
              </div>

              <div style={{ padding: 'var(--space-lg)' }}>
                <h3
                  className="text-responsive-xl font-semibold text-primary"
                  style={{ marginBottom: 'var(--space-md)' }}
                >
                  {project.title}
                </h3>
                <p
                  className="text-secondary leading-relaxed text-responsive-sm"
                  style={{ marginBottom: 'var(--space-md)' }}
                >
                  {project.description}
                </p>
                <div
                  className="flex flex-wrap"
                  style={{ gap: 'var(--space-sm)' }}
                >
                  {project.tech.map((tech, index) => (
                    <span
                      key={index}
                      className="text-brand-primary text-responsive-xs"
                      style={{
                        padding: 'var(--space-xs) var(--space-md)',
                        backgroundColor: 'var(--bg-tertiary)',
                        borderRadius: 'var(--radius-full)',
                        border: '1px solid var(--border-primary)',
                      }}
                    >
                      {tech}
                    </span>
                  ))}
                </div>
              </div>
            </motion.div>
          ))}
        </motion.div>

        <motion.div
          className="text-center"
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true }}
          variants={itemVariants}
          style={{ marginTop: 'var(--space-3xl)' }}
        >
          <motion.a
            href="https://github.com/syedshaheerabbas"
            target="_blank"
            rel="noopener noreferrer"
            className="btn-primary inline-flex items-center touch-target"
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            style={{
              textDecoration: 'none',
              gap: 'var(--space-sm)',
            }}
          >
            View More on GitHub
          </motion.a>
        </motion.div>
      </div>
    </section>
  )
}

export default Projects
